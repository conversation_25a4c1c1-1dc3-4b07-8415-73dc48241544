<?php $__env->startSection('title', 'Course Builder - ' . $course->title); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-black">
    <!-- Header -->
    <div class="bg-gradient-to-br from-black via-gray-900 to-black border-b border-gray-800">
        <div class="max-w-full px-4 sm:px-6 lg:px-8">
            <div class="py-4">
                <!-- Mobile/Tablet/Desktop Header Layout -->
                <div class="flex flex-col space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                    <!-- Mobile/Tablet: Separate rows for better space management -->
                    <div class="lg:hidden">
                        <!-- Mobile/Tablet Top Row: Back button and Menu Toggle -->
                        <div class="flex items-center justify-between mb-2">
                            <a href="<?php echo e(route('instructor.courses.index')); ?>"
                               class="text-gray-400 hover:text-white transition-colors text-sm flex items-center">
                                <i class="fas fa-arrow-left mr-1"></i>
                                <span>Back</span>
                            </a>
                            <button id="mobile-sidebar-toggle"
                                    class="text-gray-400 hover:text-white transition-colors p-2">
                                <i class="fas fa-bars text-lg"></i>
                            </button>
                        </div>
                        <!-- Mobile/Tablet Title Row: Full width for title -->
                        <div class="w-full">
                            <h1 class="text-lg md:text-xl font-bold text-white truncate pr-4"><?php echo e($course->title); ?></h1>
                        </div>
                    </div>

                    <!-- Desktop Layout: Traditional horizontal layout -->
                    <div class="hidden lg:flex lg:items-center lg:space-x-4">
                        <a href="<?php echo e(route('instructor.courses.index')); ?>"
                           class="text-gray-400 hover:text-white transition-colors text-base">
                            <i class="fas fa-arrow-left mr-2"></i>
                            <span>Back to Courses</span>
                        </a>
                        <div class="h-6 w-px bg-gray-700"></div>
                        <h1 class="text-xl font-bold text-white"><?php echo e($course->title); ?></h1>
                    </div>

                    <!-- Action Buttons Row: Save Status and Publish Button -->
                    <div class="flex items-center justify-end space-x-2 md:space-x-3">
                        <!-- Save status indicator -->
                        <div id="save-status-indicator" class="hidden px-2 py-1 md:px-3 md:py-1 rounded-lg text-xs md:text-sm font-medium">
                            <i class="fas fa-check-circle mr-1 md:mr-2"></i>
                            <span id="save-status-text" class="hidden sm:inline">Changes saved</span>
                            <span class="sm:hidden">Saved</span>
                        </div>

                        <!-- Publish/Unpublish button -->
                        <button id="publish-toggle-btn"
                                class="px-3 py-2 md:px-4 md:py-2 rounded-lg text-sm md:text-base font-medium transition-colors <?php echo e($course->status === 'published' ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'); ?>"
                                data-course-id="<?php echo e($course->id); ?>"
                                data-current-status="<?php echo e($course->status); ?>">
                            <i class="fas <?php echo e($course->status === 'published' ? 'fa-eye' : 'fa-eye-slash'); ?> mr-1 md:mr-2"></i>
                            <span class="hidden sm:inline"><?php echo e($course->status === 'published' ? 'Published' : 'Publish Course'); ?></span>
                            <span class="sm:hidden"><?php echo e($course->status === 'published' ? 'Live' : 'Publish'); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex flex-col lg:flex-row min-h-screen">
        <!-- Mobile/Tablet Sidebar Overlay -->
        <div id="mobile-sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden hidden"></div>

        <!-- Sidebar - Curriculum Panel -->
        <div id="curriculum-sidebar"
             class="fixed inset-y-0 left-0 z-50 w-80 bg-gray-900 border-r border-gray-800 overflow-y-auto transform -translate-x-full transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0 lg:w-1/3 lg:z-auto">
            <div class="p-4 lg:p-6">
                <!-- Mobile/Tablet Sidebar Header -->
                <div class="flex items-center justify-between mb-4 lg:hidden">
                    <h2 class="text-lg font-semibold text-white">Course Curriculum</h2>
                    <button id="mobile-sidebar-close" class="text-gray-400 hover:text-white transition-colors p-2">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                <!-- Desktop Sidebar Header -->
                <div class="hidden lg:flex items-center justify-between mb-6">
                    <h2 class="text-lg font-semibold text-white">Course Curriculum</h2>
                    <button id="add-chapter-btn"
                            class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Chapter
                    </button>
                </div>

                <!-- Mobile/Tablet Add Chapter Button -->
                <div class="mb-4 lg:hidden">
                    <button id="add-chapter-btn-mobile"
                            class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Chapter
                    </button>
                </div>

                <!-- Curriculum Tree -->
                <div id="curriculum-tree" class="space-y-2">
                    <?php $__empty_1 = true; $__currentLoopData = $course->chapters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chapter): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="chapter-item bg-gray-800 rounded-lg border border-gray-700" 
                             data-chapter-id="<?php echo e($chapter->id); ?>"
                             data-chapter-index="<?php echo e($loop->index); ?>">
                            <!-- Chapter Header -->
                            <div class="chapter-header p-4 cursor-pointer hover:bg-gray-750 transition-colors"
                                 onclick="selectItem('chapter', '<?php echo e($chapter->id); ?>')">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                                            <i class="fas fa-grip-vertical"></i>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-folder text-yellow-500"></i>
                                            <span class="text-white font-medium"><?php echo e($chapter->title); ?></span>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs text-gray-400"><?php echo e($chapter->lectures->count()); ?> lectures</span>
                                        <button class="text-gray-400 hover:text-white transition-colors"
                                                onclick="event.stopPropagation(); toggleChapter('<?php echo e($chapter->id); ?>')">
                                            <i class="fas fa-chevron-down chapter-toggle"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Chapter Lectures -->
                            <div class="chapter-lectures pl-8 pb-2" id="chapter-lectures-<?php echo e($chapter->id); ?>">
                                <?php $__currentLoopData = $chapter->lectures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lecture): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="lecture-item p-3 hover:bg-gray-750 rounded cursor-pointer transition-colors"
                                         data-lecture-id="<?php echo e($lecture->id); ?>"
                                         data-lecture-index="<?php echo e($loop->index); ?>"
                                         onclick="selectItem('lecture', '<?php echo e($lecture->id); ?>')">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                                                    <i class="fas fa-grip-vertical text-xs"></i>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <?php switch($lecture->type):
                                                        case ('video'): ?>
                                                            <i class="fas fa-play-circle text-blue-500"></i>
                                                            <?php break; ?>
                                                        <?php case ('text'): ?>
                                                            <i class="fas fa-file-text text-green-500"></i>
                                                            <?php break; ?>
                                                        <?php case ('quiz'): ?>
                                                            <i class="fas fa-question-circle text-purple-500"></i>
                                                            <?php break; ?>
                                                        <?php case ('assignment'): ?>
                                                            <i class="fas fa-tasks text-orange-500"></i>
                                                            <?php break; ?>
                                                        <?php case ('resource'): ?>
                                                            <i class="fas fa-download text-gray-500"></i>
                                                            <?php break; ?>
                                                        <?php default: ?>
                                                            <i class="fas fa-file text-gray-500"></i>
                                                    <?php endswitch; ?>
                                                    <span class="text-gray-300 text-sm"><?php echo e($lecture->title); ?></span>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <?php if($lecture->duration_minutes): ?>
                                                    <span class="text-xs text-gray-500"><?php echo e($lecture->duration_minutes); ?>min</span>
                                                <?php endif; ?>
                                                <?php if($lecture->is_free_preview): ?>
                                                    <span class="text-xs bg-green-600 text-white px-2 py-1 rounded">Preview</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                <!-- Add Lecture Button -->
                                <div class="p-3">
                                    <button class="add-lecture-btn w-full text-left text-gray-400 hover:text-white transition-colors text-sm"
                                            data-chapter-id="<?php echo e($chapter->id); ?>"
                                            onclick="addLecture('<?php echo e($chapter->id); ?>')">
                                        <i class="fas fa-plus mr-2"></i>Add Lecture
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div id="empty-curriculum" class="text-center py-12">
                            <div class="text-gray-500 mb-4">
                                <i class="fas fa-folder-open text-4xl"></i>
                            </div>
                            <p class="text-gray-400 mb-4">No chapters yet</p>
                            <button id="add-first-chapter-btn" 
                                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-plus mr-2"></i>Add Your First Chapter
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Main Editor Panel -->
        <div class="flex-1 bg-black overflow-y-auto lg:ml-0">
            <div class="p-4 lg:p-8">
                <!-- Course Overview Section -->
                <div id="course-overview" class="mb-6 md:mb-8">
                    <div class="bg-gradient-to-r from-gray-900 to-gray-800 border border-gray-700 rounded-lg p-4 md:p-6">
                        <!-- Mobile Layout -->
                        <div class="block md:hidden">
                            <div class="mb-4">
                                <h2 class="text-xl font-bold text-white mb-2"><?php echo e($course->title); ?></h2>
                                <?php if($course->subtitle): ?>
                                    <p class="text-gray-300 mb-3 text-sm"><?php echo e($course->subtitle); ?></p>
                                <?php endif; ?>
                            </div>

                            <!-- Mobile Stats Grid -->
                            <div class="grid grid-cols-2 gap-3 mb-4 text-xs text-gray-400">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-tag"></i>
                                    <span class="truncate"><?php echo e($course->category->name ?? 'No Category'); ?></span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-signal"></i>
                                    <span class="capitalize"><?php echo e(str_replace('_', ' ', $course->level)); ?></span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-book"></i>
                                    <span><?php echo e($course->chapters->count()); ?> Chapters</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-play-circle"></i>
                                    <span><?php echo e($course->lectures->count()); ?> Lectures</span>
                                </div>
                            </div>

                            <!-- Mobile Price and Edit Button -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <?php if($course->price > 0): ?>
                                        <span class="text-xl font-bold text-green-400">$<?php echo e(number_format($course->price, 2)); ?></span>
                                        <div class="text-xs text-gray-400">Paid Course</div>
                                    <?php else: ?>
                                        <span class="text-xl font-bold text-blue-400">FREE</span>
                                        <div class="text-xs text-gray-400">Free Course</div>
                                    <?php endif; ?>
                                </div>
                                <button onclick="selectItem('course', '<?php echo e($course->id); ?>')"
                                        class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                    <i class="fas fa-edit mr-1"></i>Edit
                                </button>
                            </div>
                        </div>

                        <!-- Desktop Layout -->
                        <div class="hidden md:block">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h2 class="text-2xl font-bold text-white mb-2"><?php echo e($course->title); ?></h2>
                                    <?php if($course->subtitle): ?>
                                        <p class="text-gray-300 mb-3"><?php echo e($course->subtitle); ?></p>
                                    <?php endif; ?>
                                    <div class="flex items-center space-x-6 text-sm text-gray-400">
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-tag"></i>
                                            <span><?php echo e($course->category->name ?? 'No Category'); ?></span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-signal"></i>
                                            <span class="capitalize"><?php echo e(str_replace('_', ' ', $course->level)); ?></span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-book"></i>
                                            <span><?php echo e($course->chapters->count()); ?> Chapters</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-play-circle"></i>
                                            <span><?php echo e($course->lectures->count()); ?> Lectures</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="mb-2">
                                        <?php if($course->price > 0): ?>
                                            <span class="text-2xl font-bold text-green-400">$<?php echo e(number_format($course->price, 2)); ?></span>
                                            <div class="text-xs text-gray-400">Paid Course</div>
                                        <?php else: ?>
                                            <span class="text-2xl font-bold text-blue-400">FREE</span>
                                            <div class="text-xs text-gray-400">Free Course</div>
                                        <?php endif; ?>
                                    </div>
                                    <button onclick="selectItem('course', '<?php echo e($course->id); ?>')"
                                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-edit mr-2"></i>Edit Details
                                    </button>
                                </div>
                            </div>
                        </div>

                        <?php if($course->description): ?>
                            <div class="border-t border-gray-700 pt-4">
                                <p class="text-gray-300 text-sm leading-relaxed"><?php echo e(Str::limit($course->description, 200)); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Welcome State -->
                <div id="welcome-state" class="<?php echo e($course->chapters->count() > 0 ? 'hidden' : ''); ?>">
                    <div class="text-center py-16">
                        <div class="text-gray-600 mb-6">
                            <i class="fas fa-edit text-6xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-white mb-4">Welcome to Course Builder</h3>
                        <p class="text-gray-400 mb-8 max-w-md mx-auto">
                            Create engaging course content with our intuitive builder.
                            Start by adding your first chapter from the sidebar.
                        </p>
                        <div class="space-y-4">
                            <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 text-left max-w-md mx-auto">
                                <h4 class="text-white font-medium mb-2">
                                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                    Pro Tips
                                </h4>
                                <ul class="text-gray-400 text-sm space-y-1">
                                    <li>• Organize content into logical chapters</li>
                                    <li>• Mix different content types for engagement</li>
                                    <li>• Use auto-save - no manual saving needed!</li>
                                    <li>• Drag and drop to reorder content</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Course Details Editor -->
                <div id="course-editor" class="hidden">
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-4 md:p-6">
                        <h3 class="text-lg md:text-xl font-bold text-white mb-4 md:mb-6">Course Details</h3>

                        <form id="course-details-form" class="space-y-4 md:space-y-6">
                            <?php echo csrf_field(); ?>
                            <!-- Mobile: Stack all fields, Desktop: Two columns for title/subtitle -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Course Title <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" name="title" value="<?php echo e($course->title); ?>"
                                           class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                           placeholder="Enter course title">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Course Subtitle
                                    </label>
                                    <input type="text" name="subtitle" value="<?php echo e($course->subtitle); ?>"
                                           class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                           placeholder="Enter course subtitle">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Course Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="description" rows="4"
                                          class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                          placeholder="Describe what students will learn in this course"><?php echo e($course->description); ?></textarea>
                            </div>

                            <!-- Mobile: Stack all fields, Desktop: Three columns -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Category <span class="text-red-500">*</span>
                                    </label>
                                    <select name="category_id"
                                            class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base">
                                        <option value="">Select Category</option>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>" <?php echo e($course->category_id == $category->id ? 'selected' : ''); ?>>
                                                <?php echo e($category->name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Level <span class="text-red-500">*</span>
                                    </label>
                                    <select name="level"
                                            class="w-full px-3 py-3 md:px-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base">
                                        <option value="beginner" <?php echo e($course->level == 'beginner' ? 'selected' : ''); ?>>Beginner</option>
                                        <option value="intermediate" <?php echo e($course->level == 'intermediate' ? 'selected' : ''); ?>>Intermediate</option>
                                        <option value="advanced" <?php echo e($course->level == 'advanced' ? 'selected' : ''); ?>>Advanced</option>
                                        <option value="all_levels" <?php echo e($course->level == 'all_levels' ? 'selected' : ''); ?>>All Levels</option>
                                    </select>
                                </div>

                                <div class="md:col-span-2 lg:col-span-1">
                                    <label class="block text-sm font-medium text-gray-300 mb-2">
                                        Price <span class="text-red-500">*</span>
                                    </label>
                                    <div class="relative">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</span>
                                        <input type="number" name="price" value="<?php echo e($course->price); ?>" step="0.01" min="0" max="999.99"
                                               class="w-full pl-8 pr-3 py-3 md:pr-4 md:py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500 text-sm md:text-base"
                                               placeholder="0.00">
                                    </div>
                                </div>
                            </div>

                            <!-- Save Button -->
                            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                                <button type="button" onclick="saveCourse()"
                                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                                    <i class="fas fa-save mr-2"></i>Save Course Details
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Chapter Editor -->
                <div id="chapter-editor" class="hidden">
                    <!-- Chapter editor content will be loaded dynamically -->
                </div>

                <!-- Lecture Editor -->
                <div id="lecture-editor" class="hidden">
                    <!-- Lecture editor content will be loaded dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSRF Token for AJAX requests -->
<meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    const courseId = '<?php echo e($course->slug); ?>';
    let currentSelection = { type: null, id: null };
    let autoSaveTimeouts = new Map();
    let isDragging = false;

    // Initialize the course builder
    initializeCourseBuilder();

    function initializeCourseBuilder() {
        // Initialize mobile navigation
        initializeMobileNavigation();

        // Initialize drag and drop
        initializeDragAndDrop();

        // Initialize auto-save
        initializeAutoSave();

        // Initialize event listeners
        initializeEventListeners();

        // Show course details by default if no chapters
        if (document.querySelectorAll('.chapter-item').length === 0) {
            selectItem('course', courseId);
        }
    }

    function initializeMobileNavigation() {
        const mobileToggle = document.getElementById('mobile-sidebar-toggle');
        const mobileClose = document.getElementById('mobile-sidebar-close');
        const sidebar = document.getElementById('curriculum-sidebar');
        const overlay = document.getElementById('mobile-sidebar-overlay');

        // Mobile sidebar toggle
        if (mobileToggle) {
            mobileToggle.addEventListener('click', function() {
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                document.body.classList.add('overflow-hidden');
            });
        }

        // Mobile sidebar close
        if (mobileClose) {
            mobileClose.addEventListener('click', closeMobileSidebar);
        }

        // Overlay click to close
        if (overlay) {
            overlay.addEventListener('click', closeMobileSidebar);
        }

        // Close sidebar on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !sidebar.classList.contains('-translate-x-full')) {
                closeMobileSidebar();
            }
        });

        // Auto-close sidebar on desktop resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) { // lg breakpoint
                closeMobileSidebar();
            }
        });

        function closeMobileSidebar() {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    }

    function initializeEventListeners() {
        // Add chapter buttons (desktop and mobile)
        document.getElementById('add-chapter-btn')?.addEventListener('click', addChapter);
        document.getElementById('add-chapter-btn-mobile')?.addEventListener('click', function() {
            addChapter();
            // Close mobile/tablet sidebar after adding chapter
            const sidebar = document.getElementById('curriculum-sidebar');
            const overlay = document.getElementById('mobile-sidebar-overlay');
            if (window.innerWidth < 1024) {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        });
        document.getElementById('add-first-chapter-btn')?.addEventListener('click', addChapter);

        // Publish toggle button
        document.getElementById('publish-toggle-btn')?.addEventListener('click', togglePublishStatus);

        // Prevent form submission
        document.getElementById('course-details-form')?.addEventListener('submit', function(e) {
            e.preventDefault();
        });

        // Touch-friendly interactions for mobile
        if ('ontouchstart' in window) {
            // Add touch feedback for interactive elements
            document.querySelectorAll('.chapter-item, .lecture-item').forEach(item => {
                item.addEventListener('touchstart', function() {
                    this.style.backgroundColor = 'rgba(127, 29, 29, 0.2)';
                });
                item.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 150);
                });
            });
        }
    }

    function initializeDragAndDrop() {
        // Make chapters sortable
        const curriculumTree = document.getElementById('curriculum-tree');
        if (curriculumTree) {
            new Sortable(curriculumTree, {
                handle: '.drag-handle',
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                onStart: function() {
                    isDragging = true;
                },
                onEnd: function(evt) {
                    isDragging = false;
                    if (evt.oldIndex !== evt.newIndex) {
                        reorderChapters();
                    }
                }
            });
        }

        // Make lectures sortable within each chapter
        document.querySelectorAll('.chapter-lectures').forEach(lecturesContainer => {
            new Sortable(lecturesContainer, {
                handle: '.drag-handle',
                animation: 150,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                filter: '.add-lecture-btn',
                onStart: function() {
                    isDragging = true;
                },
                onEnd: function(evt) {
                    isDragging = false;
                    if (evt.oldIndex !== evt.newIndex) {
                        const chapterId = lecturesContainer.id.replace('chapter-lectures-', '');
                        reorderLectures(chapterId);
                    }
                }
            });
        });
    }

    function initializeAutoSave() {
        // Auto-save disabled - using manual save instead
        // initializeCourseAutoSave();
    }

    function initializeCourseAutoSave() {
        // Auto-save for course details
        const courseForm = document.getElementById('course-details-form');
        if (courseForm) {
            const inputs = courseForm.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                // Remove existing listeners to prevent duplicates
                input.removeEventListener('input', input._autoSaveHandler);
                input.removeEventListener('change', input._autoSaveHandler);

                const eventType = input.tagName === 'SELECT' ? 'change' : 'input';
                const handler = function() {
                    scheduleAutoSave('course', courseId, () => autoSaveCourse());
                };

                // Store handler reference for removal
                input._autoSaveHandler = handler;
                input.addEventListener(eventType, handler);
            });
        }
    }

    function scheduleAutoSave(type, id, saveFunction) {
        const key = `${type}-${id}`;

        // Clear existing timeout
        if (autoSaveTimeouts.has(key)) {
            clearTimeout(autoSaveTimeouts.get(key));
        }

        // Show saving indicator
        showSaveStatus('saving', 'Saving...');

        // Schedule new save
        const timeoutId = setTimeout(() => {
            saveFunction();
            autoSaveTimeouts.delete(key);
        }, 1000); // 1 second delay

        autoSaveTimeouts.set(key, timeoutId);
    }

    function showSaveStatus(status, message) {
        const indicator = document.getElementById('auto-save-indicator');
        const statusText = document.getElementById('save-status');

        if (!indicator || !statusText) return;

        // Remove existing status classes
        indicator.className = indicator.className.replace(/bg-\w+-\d+/g, '').replace(/text-\w+-\d+/g, '');

        switch (status) {
            case 'saving':
                indicator.className += ' bg-blue-600 text-white';
                statusText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' + message;
                break;
            case 'saved':
                indicator.className += ' bg-green-600 text-white';
                statusText.innerHTML = '<i class="fas fa-check mr-2"></i>' + message;
                break;
            case 'error':
                indicator.className += ' bg-red-600 text-white';
                statusText.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + message;
                break;
        }

        indicator.classList.remove('hidden');

        // Auto-hide success/error messages
        if (status !== 'saving') {
            setTimeout(() => {
                indicator.classList.add('hidden');
            }, 3000);
        }
    }

    function showFileUploadIndicator(lectureId, show) {
        const indicator = document.getElementById(`file-upload-indicator-${lectureId}`);
        if (!indicator) return;

        if (show) {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }

    function showMultipleFileUploadIndicator(lectureId, fileCount, isUploading) {
        const lectureCard = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        if (!lectureCard) return;

        const fileUploadSection = lectureCard.querySelector('.file-upload-section');
        if (!fileUploadSection) return;

        let indicator = fileUploadSection.querySelector('.upload-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'upload-indicator';
            fileUploadSection.appendChild(indicator);
        }

        if (isUploading) {
            indicator.innerHTML = `
                <div class="multiple-upload-progress">
                    <i class="fas fa-spinner fa-spin"></i>
                    Uploading ${fileCount} file${fileCount > 1 ? 's' : ''}...
                    <div class="progress-bar-container" style="width: 100%; height: 4px; background: #e9ecef; border-radius: 2px; margin-top: 5px;">
                        <div class="progress-bar" id="progress-${lectureId}" style="height: 100%; background: #007bff; border-radius: 2px; width: 0%; transition: width 0.3s ease;"></div>
                    </div>
                </div>
            `;
            indicator.style.color = '#007bff';
            indicator.style.display = 'block';
        } else {
            indicator.style.display = 'none';
        }
    }

    function updateFileUploadProgress(lectureId, progress) {
        const progressBar = document.getElementById(`progress-${lectureId}`);
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.style.backgroundColor = progress === 100 ? '#28a745' : '#007bff';
        }
    }

    function selectItem(type, id) {
        if (isDragging) return; // Don't select during drag operations

        currentSelection = { type, id };

        // Update sidebar selection
        updateSidebarSelection(type, id);

        // Load content in main editor
        loadEditor(type, id);

        // Close mobile/tablet sidebar when selecting an item
        if (window.innerWidth < 1024) {
            const sidebar = document.getElementById('curriculum-sidebar');
            const overlay = document.getElementById('mobile-sidebar-overlay');
            if (sidebar && overlay) {
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        }
    }

    function updateSidebarSelection(type, id) {
        // Remove existing selections
        document.querySelectorAll('.chapter-item, .lecture-item').forEach(item => {
            item.classList.remove('bg-red-900', 'border-red-600');
        });

        // Add selection to current item
        if (type === 'chapter') {
            const chapterItem = document.querySelector(`[data-chapter-id="${id}"]`);
            if (chapterItem) {
                chapterItem.classList.add('bg-red-900', 'border-red-600');
            }
        } else if (type === 'lecture') {
            const lectureItem = document.querySelector(`[data-lecture-id="${id}"]`);
            if (lectureItem) {
                lectureItem.classList.add('bg-red-900', 'border-red-600');
            }
        }
    }

    function loadEditor(type, id) {
        // Hide all editors
        document.getElementById('welcome-state')?.classList.add('hidden');
        document.getElementById('course-editor')?.classList.add('hidden');
        document.getElementById('chapter-editor')?.classList.add('hidden');
        document.getElementById('lecture-editor')?.classList.add('hidden');

        switch (type) {
            case 'course':
                document.getElementById('course-editor')?.classList.remove('hidden');
                // Auto-save disabled - using manual save instead
                // initializeCourseAutoSave();
                break;
            case 'chapter':
                loadChapterEditor(id);
                break;
            case 'lecture':
                loadLectureEditor(id);
                break;
        }
    }

    function loadChapterEditor(chapterId) {
        const chapterEditor = document.getElementById('chapter-editor');
        if (!chapterEditor) return;

        // Show loading state
        chapterEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mb-4"></i>
                    <p class="text-gray-400">Loading chapter...</p>
                </div>
            </div>
        `;
        chapterEditor.classList.remove('hidden');

        // Fetch chapter data from backend
        fetch(`/instructor/courses/${courseId}/chapters/${chapterId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderChapterEditor(chapterId, data.data);
            } else {
                chapterEditor.innerHTML = `
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="text-center py-8">
                            <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                            <p class="text-red-400">Failed to load chapter data</p>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading chapter:', error);
            chapterEditor.innerHTML = `
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-400 mb-4"></i>
                        <p class="text-red-400">Network error loading chapter</p>
                    </div>
                </div>
            `;
        });
    }

    function renderChapterEditor(chapterId, chapterData) {
        const chapterEditor = document.getElementById('chapter-editor');
        if (!chapterEditor) return;

        const learningObjectives = Array.isArray(chapterData.learning_objectives)
            ? chapterData.learning_objectives.join('\n')
            : '';

        chapterEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-white">Edit Chapter</h3>
                    <button onclick="deleteChapter('${chapterId}')"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-trash mr-2"></i>Delete Chapter
                    </button>
                </div>

                <form id="chapter-form-${chapterId}" class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Chapter Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" value="${chapterData.title || ''}"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                               placeholder="Enter chapter title">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Chapter Description
                        </label>
                        <textarea name="description" rows="4"
                                  class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                  placeholder="Describe what this chapter covers">${chapterData.description || ''}</textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Learning Objectives
                        </label>
                        <textarea name="learning_objectives" rows="4"
                                  class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                  placeholder="Enter learning objectives (one per line)">${learningObjectives}</textarea>
                        <p class="text-xs text-gray-400 mt-1">Enter each learning objective on a new line</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_published" ${chapterData.is_published ? 'checked' : ''}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Published</span>
                            </label>
                        </div>

                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_free_preview" ${chapterData.is_free_preview ? 'checked' : ''}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Free Preview</span>
                            </label>
                        </div>
                    </div>
                    <!-- Save Button -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                        <button type="button" onclick="saveChapter('${chapterId}')"
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                            <i class="fas fa-save mr-2"></i>Save Chapter
                        </button>
                    </div>
                </form>
            </div>
        `;

        // Remove auto-save initialization
        // initializeChapterAutoSave(chapterId);
    }

    function initializeChapterAutoSave(chapterId) {
        const chapterForm = document.getElementById(`chapter-form-${chapterId}`);
        if (chapterForm) {
            const inputs = chapterForm.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                // Remove existing listeners to prevent duplicates
                input.removeEventListener('input', input._autoSaveHandler);
                input.removeEventListener('change', input._autoSaveHandler);

                const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';
                const handler = function() {
                    scheduleAutoSave('chapter', chapterId, () => autoSaveChapter(chapterId));
                };

                // Store handler reference for removal
                input._autoSaveHandler = handler;
                input.addEventListener(eventType, handler);
            });
        }
    }

    function initializeLectureAutoSave(lectureId) {
        const lectureForm = document.getElementById(`lecture-form-${lectureId}`);
        if (!lectureForm) {
            console.error('Lecture form not found for auto-save initialization:', lectureId);
            return;
        }

        // Get all form inputs, including those in hidden content sections
        const inputs = lectureForm.querySelectorAll('input, textarea, select');

        inputs.forEach(input => {
            // Remove existing listeners to prevent duplicates
            if (input._lectureAutoSaveHandler) {
                input.removeEventListener('input', input._lectureAutoSaveHandler);
                input.removeEventListener('change', input._lectureAutoSaveHandler);
            }

            // Initialize FileManager for file inputs
            if (input.type === 'file' && input.name === 'resource_file') {
                // Initialize FileManager for this lecture if not already done
                if (!window.fileManagers[lectureId]) {
                    console.log('Creating new FileManager for lecture:', lectureId);
                    window.fileManagers[lectureId] = new FileManager(lectureId);
                } else {
                    console.log('FileManager already exists for lecture:', lectureId);
                }

                // The FileManager handles all file events, so we don't need additional handlers here
                console.log('FileManager setup complete for lecture:', lectureId);
            } else {
                const eventType = input.type === 'checkbox' || input.tagName === 'SELECT' ? 'change' : 'input';
                const handler = function() {
                    // Add a small delay to ensure the form state is updated
                    setTimeout(() => {
                        scheduleAutoSave('lecture', lectureId, () => autoSaveLecture(lectureId));
                    }, 50);
                };

                // Store handler reference for removal
                input._lectureAutoSaveHandler = handler;
                input.addEventListener(eventType, handler);
            }
        });

        console.log(`Auto-save initialized for lecture ${lectureId} with ${inputs.length} form fields`);
    }

    function loadLectureEditor(lectureId) {
        const lectureEditor = document.getElementById('lecture-editor');
        if (!lectureEditor) return;

        // Show loading state
        lectureEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
                    <span class="ml-3 text-gray-300">Loading lecture data...</span>
                </div>
            </div>
        `;

        // Get chapter ID from the lecture element
        const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        const chapterElement = lectureElement?.closest('.chapter-item');
        const chapterId = chapterElement?.getAttribute('data-chapter-id');

        if (!chapterId) {
            console.error('Chapter ID not found for lecture:', lectureId);
            return;
        }

        // Fetch lecture data from server
        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderLectureEditor(lectureId, data.lecture);
            } else {
                console.error('Failed to load lecture data:', data.message);
                lectureEditor.innerHTML = `
                    <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                        <div class="text-center py-8">
                            <div class="text-red-400 mb-2">
                                <i class="fas fa-exclamation-triangle text-2xl"></i>
                            </div>
                            <p class="text-gray-300">Failed to load lecture data</p>
                            <p class="text-sm text-gray-400">${data.message || 'Unknown error'}</p>
                        </div>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading lecture data:', error);
            lectureEditor.innerHTML = `
                <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                    <div class="text-center py-8">
                        <div class="text-red-400 mb-2">
                            <i class="fas fa-exclamation-triangle text-2xl"></i>
                        </div>
                        <p class="text-gray-300">Network error loading lecture data</p>
                    </div>
                </div>
            `;
        });
    }

    function renderLectureEditor(lectureId, lecture) {
        const lectureEditor = document.getElementById('lecture-editor');
        if (!lectureEditor) return;

        // Extract data from lecture object for form population
        const lectureTitle = lecture.title || '';
        const lectureType = lecture.type || 'text';
        const lectureDescription = lecture.description || '';
        const videoUrl = lecture.video_url || '';
        const durationMinutes = lecture.duration_minutes || '';
        const content = lecture.content || '';
        const estimatedCompletionMinutes = lecture.estimated_completion_minutes || '';
        const quizPassingScore = lecture.quiz_passing_score || 70;
        const quizAllowRetakes = lecture.quiz_allow_retakes ? 'checked' : '';
        const isPublished = lecture.is_published ? 'checked' : '';
        const isFreePreview = lecture.is_free_preview ? 'checked' : '';
        const isMandatory = lecture.is_mandatory ? 'checked' : '';

        // Extract type-specific data from JSON fields
        const quizInstructions = lecture.quiz_data?.instructions || '';
        const assignmentInstructions = lecture.attachments?.instructions || '';
        const assignmentMaxPoints = lecture.attachments?.max_points || 100;
        const assignmentDueDate = lecture.attachments?.due_date || '';
        const resourceDescription = lecture.resources?.description || '';
        const resourceUrl = lecture.resources?.url || '';
        const resourceFiles = lecture.resources?.files || [];
        
        // Debug logging
        console.log('Lecture resources data:', lecture.resources);
        console.log('Resource files:', resourceFiles);

        lectureEditor.innerHTML = `
            <div class="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-white">Edit Lecture</h3>
                    <button onclick="deleteLecture('${lectureId}')"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-trash mr-2"></i>Delete Lecture
                    </button>
                </div>

                <form id="lecture-form-${lectureId}" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Lecture Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="title" value="${lectureTitle}"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                   placeholder="Enter lecture title">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Lecture Type <span class="text-red-500">*</span>
                            </label>
                            <select name="type" onchange="toggleLectureContent('${lectureId}', this.value)"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:border-red-500 focus:ring-1 focus:ring-red-500">
                                <option value="video" ${lectureType === 'video' ? 'selected' : ''}>Video</option>
                                <option value="text" ${lectureType === 'text' ? 'selected' : ''}>Text/Article</option>
                                <option value="quiz" ${lectureType === 'quiz' ? 'selected' : ''}>Quiz</option>
                                <option value="assignment" ${lectureType === 'assignment' ? 'selected' : ''}>Assignment</option>
                                <option value="resource" ${lectureType === 'resource' ? 'selected' : ''}>Resource</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Description
                        </label>
                        <textarea name="description" rows="3"
                                  class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                  placeholder="Describe what students will learn in this lecture">${lectureDescription}</textarea>
                    </div>

                    <!-- Video Content -->
                    <div id="video-content-${lectureId}" class="lecture-content ${lectureType === 'video' ? '' : 'hidden'}">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Video URL
                                </label>
                                <input type="url" name="video_url" value="${videoUrl}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                       placeholder="https://youtube.com/watch?v=...">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Duration (minutes)
                                </label>
                                <input type="number" name="duration_minutes" min="0" max="1440" value="${durationMinutes}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                       placeholder="0">
                            </div>
                        </div>
                    </div>

                    <!-- Text Content -->
                    <div id="text-content-${lectureId}" class="lecture-content ${lectureType === 'text' ? '' : 'hidden'}">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Content
                            </label>
                            <textarea name="content" rows="8"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                      placeholder="Write your lesson content here...">${content}</textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Estimated Reading Time (minutes)
                            </label>
                            <input type="number" name="estimated_completion_minutes" min="0" max="1440" value="${estimatedCompletionMinutes}"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                   placeholder="0">
                        </div>
                    </div>

                    <!-- Quiz Content -->
                    <div id="quiz-content-${lectureId}" class="lecture-content ${lectureType === 'quiz' ? '' : 'hidden'}">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Passing Score (%)
                                </label>
                                <input type="number" name="quiz_passing_score" min="0" max="100" value="${quizPassingScore}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500">
                            </div>

                            <div>
                                <label class="flex items-center space-x-3 mt-8">
                                    <input type="checkbox" name="quiz_allow_retakes" ${quizAllowRetakes}
                                           class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                    <span class="text-gray-300">Allow Retakes</span>
                                </label>
                            </div>
                        </div>

                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Quiz Instructions
                            </label>
                            <textarea name="quiz_instructions" rows="4"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                      placeholder="Instructions for students taking this quiz...">${quizInstructions}</textarea>
                        </div>
                    </div>

                    <!-- Assignment Content -->
                    <div id="assignment-content-${lectureId}" class="lecture-content ${lectureType === 'assignment' ? '' : 'hidden'}">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Assignment Instructions
                            </label>
                            <textarea name="assignment_instructions" rows="8"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                      placeholder="Detailed instructions for the assignment...">${assignmentInstructions}</textarea>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Maximum Points
                                </label>
                                <input type="number" name="assignment_max_points" min="0" max="1000" value="${assignmentMaxPoints}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">
                                    Due Date (optional)
                                </label>
                                <input type="datetime-local" name="assignment_due_date" value="${assignmentDueDate}"
                                       class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500">
                            </div>
                        </div>
                    </div>

                    <!-- Resource Content -->
                    <div id="resource-content-${lectureId}" class="lecture-content ${lectureType === 'resource' ? '' : 'hidden'}">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Resource Description
                            </label>
                            <textarea name="resource_description" rows="4"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                      placeholder="Describe what students will find in this resource...">${resourceDescription}</textarea>
                        </div>

                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Resource URL (optional)
                            </label>
                            <input type="url" name="resource_url" value="${resourceUrl}"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-red-500 focus:ring-1 focus:ring-red-500"
                                   placeholder="https://example.com/resource">
                        </div>

                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-300 mb-3 flex items-center">
                                <i class="fas fa-plus-circle mr-2 text-green-400 text-lg"></i>
                                Add Files (optional)
                            </label>
                            <div class="relative">
                                <!-- Enhanced file input with prominent styling -->
                                <div class="file-input-wrapper relative border-2 border-dashed border-gray-600 hover:border-green-500 rounded-xl p-6 bg-gradient-to-br from-gray-800 to-gray-750 hover:from-gray-750 hover:to-gray-700 transition-all duration-300 cursor-pointer group">
                                    <input type="file" name="resource_file" multiple
                                           class="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                                           accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.jpg,.jpeg,.png,.gif,.webp">

                                    <div class="text-center">
                                        <div class="mb-4">
                                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 group-hover:text-green-400 transition-colors duration-300"></i>
                                        </div>
                                        <p class="text-lg font-semibold text-gray-300 group-hover:text-white transition-colors duration-300 mb-2">
                                            <i class="fas fa-plus mr-2 text-green-400"></i>
                                            Click to select files or drag & drop
                                        </p>
                                        <p class="text-sm text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                                            Multiple files supported
                                        </p>
                                    </div>

                                    <!-- Local upload indicator -->
                                    <div id="file-upload-indicator-${lectureId}" class="hidden absolute top-3 right-3 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg flex items-center shadow-lg">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>
                                        <span>Uploading...</span>
                                    </div>
                                </div>
                            </div>
                            <p class="text-xs text-gray-400 mt-2 flex items-center">
                                <i class="fas fa-info-circle mr-1"></i>
                                Supported formats: PDF, Word, Excel, PowerPoint, ZIP, RAR, Images
                            </p>

                            <!-- File Preview Container -->
                            <div id="file-preview-container-${lectureId}" class="mt-3"></div>
                            
                            ${resourceFiles.length > 0 ? `
                                <div class="uploaded-files-display mt-4">
                                    <div class="uploaded-files-list bg-gradient-to-r from-gray-800 to-gray-750 border border-gray-600 rounded-xl p-5 shadow-lg">
                                        <h6 class="text-base font-semibold text-gray-200 mb-4 flex items-center">
                                            <i class="fas fa-cloud-upload-alt mr-2 text-green-400 text-lg"></i>
                                            Uploaded Files (${resourceFiles.length})
                                        </h6>
                                        <div class="space-y-3">
                                            ${resourceFiles.map(file => `
                                                <div class="file-item flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 bg-gradient-to-r from-gray-700 to-gray-650 rounded-xl hover:from-gray-650 hover:to-gray-600 transition-all duration-300 hover:shadow-md border border-gray-600/50 hover:border-gray-500">
                                                    <div class="flex items-center space-x-4 mb-3 sm:mb-0">
                                                        <div class="w-12 h-12 bg-gray-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                            <i class="fas fa-file text-blue-400 text-lg"></i>
                                                        </div>
                                                        <div>
                                                            <p class="text-sm text-white font-medium truncate">${file.name}</p>
                                                            <p class="text-xs text-gray-400">${(file.file_size / 1024 / 1024).toFixed(2)} MB • Uploaded</p>
                                                        </div>
                                                    </div>
                                                    <div class="flex items-center space-x-3 ml-16 sm:ml-0">
                                                        <a href="/instructor/files/resources/view?path=${encodeURIComponent(file.file_path)}"
                                                           target="_blank"
                                                           class="text-blue-400 hover:text-blue-300 text-sm px-3 py-2 rounded-lg hover:bg-blue-900/20 transition-all duration-200 flex items-center border border-blue-500/30 hover:border-blue-400">
                                                            <i class="fas fa-eye mr-2 text-lg"></i><span class="hidden sm:inline">View</span>
                                                        </a>
                                                        <a href="/instructor/files/resources/download?path=${encodeURIComponent(file.file_path)}"
                                                           class="text-green-400 hover:text-green-300 text-sm px-3 py-2 rounded-lg hover:bg-green-900/20 transition-all duration-200 flex items-center border border-green-500/30 hover:border-green-400">
                                                            <i class="fas fa-download mr-2 text-lg"></i><span class="hidden sm:inline">Download</span>
                                                        </a>
                                                        <button onclick="confirmRemoveFile('${lectureId}', '${file.id || file.name || 'unknown'}', '${file.name}')"
                                                                class="text-red-400 hover:text-red-300 text-sm px-4 py-2 rounded-lg hover:bg-red-900/20 transition-all duration-200 flex items-center border border-red-500/30 hover:border-red-400 hover:scale-105 transform shadow-sm">
                                                            <i class="fas fa-trash-alt mr-2 text-lg"></i><span class="hidden sm:inline font-medium">Remove</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_published" ${isPublished}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Published</span>
                            </label>
                        </div>

                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_free_preview" ${isFreePreview}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Free Preview</span>
                            </label>
                        </div>

                        <div>
                            <label class="flex items-center space-x-3">
                                <input type="checkbox" name="is_mandatory" ${isMandatory}
                                       class="w-4 h-4 text-red-600 bg-gray-800 border-gray-600 rounded focus:ring-red-500">
                                <span class="text-gray-300">Mandatory</span>
                            </label>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-700">
                        <button type="button" onclick="saveLecture('${lectureId}')"
                                class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center">
                            <i class="fas fa-save mr-2"></i>Save Lecture
                        </button>
                    </div>
                </form>
            </div>
        `;

        lectureEditor.classList.remove('hidden');

        // Remove auto-save initialization
        // initializeLectureAutoSave(lectureId);
    }

    function toggleLectureContent(lectureId, type) {
        console.log(`Toggling lecture content for lecture ${lectureId} to type: ${type}`);

        // Hide all content sections
        const contentSections = document.querySelectorAll(`#lecture-form-${lectureId} .lecture-content`);
        console.log(`Found ${contentSections.length} content sections to hide`);
        contentSections.forEach(section => {
            section.classList.add('hidden');
        });

        // Show selected content section
        const targetSection = document.getElementById(`${type}-content-${lectureId}`);
        if (targetSection) {
            targetSection.classList.remove('hidden');
            console.log(`Showed content section for type: ${type}`);
        } else {
            console.warn(`Target section not found: ${type}-content-${lectureId}`);
        }

        // Re-initialize auto-save listeners for all form fields after content toggle
        // This ensures that newly visible type-specific fields have auto-save functionality
        console.log('Re-initializing auto-save listeners after content toggle');
        initializeLectureAutoSave(lectureId);
    }

    // Save functions
    function saveCourse() {
        const form = document.getElementById('course-details-form');
        if (!form) return;

        // Show saving status
        showSaveStatus('saving', 'Saving course details...');

        const formData = new FormData(form);

        fetch(`/instructor/course-builder/${courseId}/auto-save`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveStatus('saved', 'Course details saved successfully!');
                // Update course overview section
                updateCourseOverview(data.data);
            } else {
                showSaveStatus('error', data.message || 'Save failed');
            }
        })
        .catch(error => {
            console.error('Save error:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function autoSaveCourse() {
        // Auto-save functionality disabled - use manual save instead
        console.log('Auto-save disabled for course');
    }

    function updateCourseOverview(courseData) {
        // Update course title in overview
        const overviewTitle = document.querySelector('#course-overview h2');
        if (overviewTitle && courseData.title) {
            overviewTitle.textContent = courseData.title;
        }

        // Update course subtitle
        const overviewSubtitle = document.querySelector('#course-overview p');
        if (overviewSubtitle && courseData.subtitle) {
            overviewSubtitle.textContent = courseData.subtitle;
        }

        // Update pricing display
        const priceDisplay = document.querySelector('#course-overview .text-2xl');
        if (priceDisplay && courseData.price !== undefined) {
            if (courseData.price > 0) {
                priceDisplay.textContent = `$${parseFloat(courseData.price).toFixed(2)}`;
                priceDisplay.className = 'text-2xl font-bold text-green-400';
                const priceLabel = priceDisplay.nextElementSibling;
                if (priceLabel) priceLabel.textContent = 'Paid Course';
            } else {
                priceDisplay.textContent = 'FREE';
                priceDisplay.className = 'text-2xl font-bold text-blue-400';
                const priceLabel = priceDisplay.nextElementSibling;
                if (priceLabel) priceLabel.textContent = 'Free Course';
            }
        }

        // Update header title
        const headerTitle = document.querySelector('h1');
        if (headerTitle && courseData.title) {
            headerTitle.textContent = courseData.title;
        }
    }

    function saveChapter(chapterId) {
        const form = document.getElementById(`chapter-form-${chapterId}`);
        if (!form) return;

        // Show saving status
        showSaveStatus('saving', 'Saving chapter...');

        const formData = new FormData(form);

        // Convert learning objectives from string to array
        const learningObjectivesTextarea = form.querySelector('textarea[name="learning_objectives"]');
        if (learningObjectivesTextarea && learningObjectivesTextarea.value.trim()) {
            const objectives = learningObjectivesTextarea.value
                .split('\n')
                .map(obj => obj.trim())
                .filter(obj => obj.length > 0);

            // Remove the original field and add array values
            formData.delete('learning_objectives');
            objectives.forEach((objective, index) => {
                formData.append(`learning_objectives[${index}]`, objective);
            });
        }

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/auto-save`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveStatus('saved', 'Chapter saved successfully!');
                // Update sidebar title if changed
                const titleInput = form.querySelector('input[name="title"]');
                if (titleInput) {
                    const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);
                    const titleSpan = chapterElement?.querySelector('.chapter-header span');
                    if (titleSpan) {
                        titleSpan.textContent = titleInput.value;
                    }
                }
            } else {
                showSaveStatus('error', data.message || 'Save failed');
            }
        })
        .catch(error => {
            console.error('Save error:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function autoSaveChapter(chapterId) {
        // Auto-save functionality disabled - use manual save instead
        console.log('Auto-save disabled for chapter:', chapterId);
    }

    function saveLecture(lectureId) {
        const form = document.getElementById(`lecture-form-${lectureId}`);
        if (!form) {
            console.error('Lecture form not found:', lectureId);
            return;
        }

        // Get chapter ID from the lecture element
        const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        const chapterElement = lectureElement?.closest('.chapter-item');
        const chapterId = chapterElement?.getAttribute('data-chapter-id');

        if (!chapterId) {
            console.error('Chapter ID not found for lecture:', lectureId);
            return;
        }

        // Show saving status
        showSaveStatus('saving', 'Saving lecture...');

        // Create FormData and ensure all fields are captured, including those in hidden sections
        const formData = new FormData();

        // Get all form inputs, including those in hidden content sections
        const allInputs = form.querySelectorAll('input, textarea, select');

        allInputs.forEach(input => {
            if (input.name) {
                if (input.type === 'checkbox') {
                    // Handle checkboxes - send as '1' or '0'
                    formData.append(input.name, input.checked ? '1' : '0');
                } else if (input.type === 'radio') {
                    // Handle radio buttons - only send if checked
                    if (input.checked) {
                        formData.append(input.name, input.value);
                    }
                } else if (input.type === 'file') {
                    // Handle file inputs - append actual files
                    if (input.files && input.files.length > 0) {
                        console.log(`Processing file input: ${input.name}`, {
                            multiple: input.multiple,
                            fileCount: input.files.length
                        });
                        
                        if (input.multiple) {
                            // For multiple files, append each with the same name (Laravel will handle as array)
                            Array.from(input.files).forEach((file, index) => {
                                console.log(`Appending file [${index}]:`, file.name);
                                formData.append(`${input.name}[]`, file);
                            });
                        } else {
                            // Handle single file
                            console.log(`Appending single file:`, input.files[0].name);
                            formData.append(input.name, input.files[0]);
                        }
                    } else {
                        console.log(`File input ${input.name} has no files selected`);
                    }
                } else {
                    // Handle all other input types
                    formData.append(input.name, input.value || '');
                }
            }
        });

        // Log form data for debugging
        console.log('Saving lecture data:', {
            lectureId: lectureId,
            chapterId: chapterId,
            formFields: Array.from(formData.entries()).map(([key, value]) => {
                if (value instanceof File) {
                    return [key, {
                        type: 'File',
                        name: value.name,
                        size: value.size,
                        mimeType: value.type,
                        constructor: value.constructor.name,
                        lastModified: value.lastModified
                    }];
                }
                return [key, value];
            })
        });
        
        console.log('Sending FormData to URL:', `/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}/auto-save`);

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}/auto-save`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            console.log('Save response status:', response.status);
            console.log('Save response headers:', Object.fromEntries(response.headers.entries()));
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('Save HTTP error:', response.status, response.statusText, text);
                    showSaveStatus('error', `Upload failed: ${response.status} ${response.statusText}`);
                    throw new Error(`HTTP ${response.status}: ${response.statusText} - ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            // Hide file upload indicator
            showFileUploadIndicator(lectureId, false);
            
            if (data.success) {
                showSaveStatus('saved', 'Lecture saved successfully!');
                console.log('Lecture save successful:', data);

                // Update sidebar title and icon if changed
                const titleInput = form.querySelector('input[name="title"]');
                const typeSelect = form.querySelector('select[name="type"]');
                if (titleInput && lectureElement) {
                    const titleSpan = lectureElement.querySelector('span');
                    if (titleSpan) {
                        titleSpan.textContent = titleInput.value;
                    }

                    // Update icon based on type
                    if (typeSelect) {
                        const iconElement = lectureElement.querySelector('i');
                        if (iconElement) {
                            iconElement.className = getLectureIcon(typeSelect.value);
                        }
                    }
                }
            } else {
                console.error('Save failed:', data);
                showSaveStatus('error', data.message || 'Save failed');

                // Log detailed error information for debugging
                if (data.errors) {
                    console.error('Validation errors:', data.errors);
                    Object.keys(data.errors).forEach(field => {
                        console.error(`Field ${field}:`, data.errors[field]);
                    });
                }
            }
        })
        .catch(error => {
            // Hide file upload indicator
            showFileUploadIndicator(lectureId, false);
            
            console.error('Save network/fetch error:', error);
            showSaveStatus('error', 'Network error - please check your connection');
        });
    }

    function saveLectureWithRealTimeUpdates(lectureId, files) {
        console.log('Starting real-time file upload for lecture:', lectureId);

        const lecture = lectures.find(l => l.id === lectureId);
        if (!lecture) {
            console.error('Lecture not found:', lectureId);
            return;
        }

        const chapter = chapters.find(c => c.id === lecture.chapter_id);
        if (!chapter) {
            console.error('Chapter not found for lecture:', lectureId);
            return;
        }

        const formData = new FormData();
        const form = document.getElementById(`lecture-form-${lectureId}`);
        if (!form) {
            console.error('Form not found for lecture:', lectureId);
            return;
        }

        // Add all form data
        const formDataEntries = new FormData(form);
        for (let [key, value] of formDataEntries.entries()) {
            if (key !== 'resource_file') { // Skip files, we'll add them separately
                formData.append(key, value);
            }
        }

        // Add files with progress tracking
        if (files && files.length > 0) {
            for (let i = 0; i < files.length; i++) {
                formData.append('resource_file[]', files[i]);
            }
        }

        console.log('Sending real-time upload request...');

        // Create XMLHttpRequest for progress tracking
        const xhr = new XMLHttpRequest();

        // Track upload progress
        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                console.log(`Upload progress: ${percentComplete.toFixed(2)}%`);
                updateFileUploadProgress(lectureId, percentComplete);

                // Update individual file preview progress
                for (let i = 0; i < files.length; i++) {
                    updateFilePreviewProgress(lectureId, i, percentComplete);
                }
            }
        });

        // Handle completion
        xhr.addEventListener('load', function() {
            console.log('Upload completed, status:', xhr.status);

            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    console.log('Upload response:', response);

                    // Update lecture data with new file information
                    if (response.lecture) {
                        const lectureIndex = lectures.findIndex(l => l.id === lectureId);
                        if (lectureIndex !== -1) {
                            lectures[lectureIndex] = { ...lectures[lectureIndex], ...response.lecture };
                        }
                    }

                    // Show enhanced success message
                    showSaveStatus('success', `🎉 Successfully uploaded ${files.length} file${files.length > 1 ? 's' : ''}!`);

                    // Update the file display in real-time
                    updateFileDisplayRealTime(lectureId, response.files || []);

                    // Show success animation
                    showUploadSuccessAnimation(lectureId);

                    // Clear the preview container after successful upload
                    clearFilePreview(lectureId);

                } catch (error) {
                    console.error('Error parsing upload response:', error);
                    showSaveStatus('error', 'Upload completed but response parsing failed');
                }
            } else {
                console.error('Upload failed with status:', xhr.status);
                showSaveStatus('error', `Upload failed: ${xhr.status} ${xhr.statusText}`);
            }

            // Hide upload indicator
            showMultipleFileUploadIndicator(lectureId, files.length, false);
        });

        // Handle errors
        xhr.addEventListener('error', function() {
            console.error('Upload error occurred');
            showSaveStatus('error', 'Upload failed due to network error');
            showMultipleFileUploadIndicator(lectureId, files.length, false);
        });

        // Send the request
        xhr.open('POST', `/instructor/course-builder/${courseId}/chapters/${chapter.id}/lectures/${lectureId}/auto-save`);
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        xhr.send(formData);
    }

    function updateFileDisplayRealTime(lectureId, files) {
        console.log('Updating file display in real-time for lecture:', lectureId, 'files:', files);

        const lectureCard = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        if (!lectureCard) return;

        const fileDisplaySection = lectureCard.querySelector('.uploaded-files-display');
        if (!fileDisplaySection) {
            // Create file display section if it doesn't exist
            const fileUploadSection = lectureCard.querySelector('.file-upload-section');
            if (fileUploadSection) {
                const displaySection = document.createElement('div');
                displaySection.className = 'uploaded-files-display mt-3';
                fileUploadSection.appendChild(displaySection);
            }
        }

        const displaySection = lectureCard.querySelector('.uploaded-files-display');
        if (displaySection) {
            if (files.length > 0) {
                displaySection.innerHTML = `
                    <div class="uploaded-files-list bg-gradient-to-r from-gray-800 to-gray-750 border border-gray-600 rounded-xl p-5 mt-4 shadow-lg">
                        <h6 class="text-base font-semibold text-gray-200 mb-4 flex items-center">
                            <i class="fas fa-cloud-upload-alt mr-2 text-green-400"></i>
                            Uploaded Files (${files.length})
                        </h6>
                        <div class="space-y-2">
                            ${files.map((file, index) => {
                                const fileIcon = getFileTypeIconForDisplay(file.file_type, file.name);
                                const uploadDate = file.uploaded_at ? new Date(file.uploaded_at).toLocaleDateString() : 'Unknown';
                                return `
                                    <div class="file-item flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 bg-gradient-to-r from-gray-700 to-gray-650 rounded-xl hover:from-gray-650 hover:to-gray-600 transition-all duration-300 hover:shadow-md border border-gray-600/50 hover:border-gray-500">
                                        <div class="flex items-center space-x-3 mb-2 sm:mb-0">
                                            <div class="w-10 h-10 bg-gray-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                                ${fileIcon}
                                            </div>
                                            <div class="min-w-0 flex-1">
                                                <p class="text-sm text-white font-medium truncate">${file.original_name || file.name}</p>
                                                <p class="text-xs text-gray-400">${(file.file_size / 1024 / 1024).toFixed(2)} MB • Uploaded ${uploadDate}</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 ml-13 sm:ml-0">
                                            <a href="${file.download_url || '#'}"
                                               class="text-blue-400 hover:text-blue-300 text-sm px-3 py-1 rounded hover:bg-gray-600 transition-colors flex items-center"
                                               title="Download">
                                                <i class="fas fa-download mr-1"></i><span class="hidden sm:inline">Download</span>
                                            </a>
                                            <button onclick="confirmRemoveFile('${lectureId}', '${file.id}', '${file.name}')"
                                                    class="text-red-400 hover:text-red-300 text-sm px-4 py-2 rounded-lg hover:bg-red-900/20 transition-all duration-200 flex items-center border border-red-500/30 hover:border-red-400 hover:scale-105 transform shadow-sm"
                                                    title="Remove file">
                                                <i class="fas fa-trash-alt mr-2 text-lg"></i><span class="hidden sm:inline font-medium">Remove</span>
                                            </button>
                                        </div>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                `;
            } else {
                displaySection.innerHTML = '';
            }
        }
    }

    function getFileTypeIconForDisplay(fileType, fileName) {
        const extension = fileName ? fileName.split('.').pop().toLowerCase() : '';

        if (fileType && fileType.startsWith('image/')) {
            return '<i class="fas fa-image text-blue-400"></i>';
        } else if (fileType === 'application/pdf' || extension === 'pdf') {
            return '<i class="fas fa-file-pdf text-red-400"></i>';
        } else if (fileType && fileType.includes('word') || ['doc', 'docx'].includes(extension)) {
            return '<i class="fas fa-file-word text-blue-600"></i>';
        } else if (fileType && fileType.includes('excel') || ['xls', 'xlsx'].includes(extension)) {
            return '<i class="fas fa-file-excel text-green-600"></i>';
        } else if (fileType && fileType.includes('powerpoint') || ['ppt', 'pptx'].includes(extension)) {
            return '<i class="fas fa-file-powerpoint text-orange-600"></i>';
        } else if (['zip', 'rar', '7z'].includes(extension)) {
            return '<i class="fas fa-file-archive text-purple-400"></i>';
        } else if (fileType && fileType.startsWith('video/')) {
            return '<i class="fas fa-file-video text-red-500"></i>';
        } else if (fileType && fileType.startsWith('audio/')) {
            return '<i class="fas fa-file-audio text-green-500"></i>';
        } else {
            return '<i class="fas fa-file text-gray-400"></i>';
        }
    }

    function confirmRemoveFile(lectureId, fileId, fileName) {
        // Create a custom confirmation dialog with enhanced styling
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 backdrop-blur-sm';
        modal.innerHTML = `
            <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-600 rounded-xl p-6 max-w-md mx-4 w-full shadow-2xl transform transition-all duration-300 scale-95 hover:scale-100">
                <div class="flex items-center mb-4">
                    <i class="fas fa-exclamation-triangle text-yellow-400 text-xl mr-3"></i>
                    <h3 class="text-lg font-medium text-white">Confirm File Removal</h3>
                </div>
                <p class="text-gray-300 mb-6 text-sm sm:text-base">
                    Are you sure you want to remove "<strong class="break-words">${fileName}</strong>"? This action cannot be undone.
                </p>
                <div class="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3">
                    <button onclick="closeConfirmDialog()"
                            class="w-full sm:w-auto px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors">
                        Cancel
                    </button>
                    <button onclick="removeFile('${lectureId}', '${fileId}')"
                            class="w-full sm:w-auto px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-500 transition-colors">
                        Remove File
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Close on background click
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeConfirmDialog();
            }
        });
    }

    function closeConfirmDialog() {
        const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-60');
        if (modal) {
            modal.remove();
        }
    }

    function removeFile(lectureId, fileId) {
        // Close the confirmation dialog
        closeConfirmDialog();

        console.log('Removing file:', fileId, 'from lecture:', lectureId);

        const lecture = lectures.find(l => l.id === lectureId);
        if (!lecture) {
            console.error('Lecture not found:', lectureId);
            return;
        }

        const chapter = chapters.find(c => c.id === lecture.chapter_id);
        if (!chapter) {
            console.error('Chapter not found for lecture:', lectureId);
            return;
        }

        // Show loading state
        showSaveStatus('saving', 'Removing file...');

        // Call backend API to delete the file
        fetch(`/instructor/course-builder/${courseId}/chapters/${chapter.id}/lectures/${lectureId}/files/${fileId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update local lecture data
                if (lecture.resources && lecture.resources.files) {
                    lecture.resources.files = data.files;
                }

                // Update the display
                updateFileDisplayRealTime(lectureId, data.files);

                showSaveStatus('success', 'File removed successfully');
            } else {
                console.error('Failed to remove file:', data.error);
                showSaveStatus('error', data.error || 'Failed to remove file');
            }
        })
        .catch(error => {
            console.error('Error removing file:', error);
            showSaveStatus('error', 'Network error - failed to remove file');
        });
    }

    function autoSaveLecture(lectureId) {
        // Auto-save functionality disabled - use manual save instead
        console.log('Auto-save disabled for lecture:', lectureId);
    }

    function getLectureIcon(type) {
        const icons = {
            'video': 'fas fa-play-circle text-blue-500',
            'text': 'fas fa-file-text text-green-500',
            'quiz': 'fas fa-question-circle text-purple-500',
            'assignment': 'fas fa-tasks text-orange-500',
            'resource': 'fas fa-download text-gray-500'
        };
        return icons[type] || 'fas fa-file text-gray-500';
    }

    // NEW FILE MANAGEMENT SYSTEM
    class FileManager {
        constructor(lectureId) {
            this.lectureId = lectureId;
            this.selectedFiles = [];
            this.fileInput = null;
            this.previewContainer = null;
            this.dragWrapper = null;
            this.init();
        }

        init() {
            // Find elements specific to this lecture
            const lectureContent = document.getElementById(`lecture-content-${this.lectureId}`);
            if (lectureContent) {
                this.fileInput = lectureContent.querySelector(`input[name="resource_file"]`);
                this.dragWrapper = lectureContent.querySelector('.file-input-wrapper');
            } else {
                // Fallback to global selectors
                this.fileInput = document.querySelector(`input[name="resource_file"]`);
                this.dragWrapper = document.querySelector('.file-input-wrapper');
            }

            this.previewContainer = document.getElementById(`file-preview-container-${this.lectureId}`);

            console.log('FileManager init for lecture:', this.lectureId, {
                lectureContent: !!lectureContent,
                fileInput: !!this.fileInput,
                previewContainer: !!this.previewContainer,
                dragWrapper: !!this.dragWrapper
            });

            if (!this.fileInput || !this.previewContainer || !this.dragWrapper) {
                console.error('FileManager: Required elements not found', {
                    lectureContent: !!lectureContent,
                    fileInput: !!this.fileInput,
                    previewContainer: !!this.previewContainer,
                    dragWrapper: !!this.dragWrapper
                });
                return;
            }

            this.setupEventListeners();
            console.log('FileManager successfully initialized for lecture:', this.lectureId);
        }

        setupEventListeners() {
            // File input change event
            this.fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files);
            });

            // Drag and drop events
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                this.dragWrapper.addEventListener(eventName, this.preventDefaults.bind(this), false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                this.dragWrapper.addEventListener(eventName, this.highlight.bind(this), false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                this.dragWrapper.addEventListener(eventName, this.unhighlight.bind(this), false);
            });

            this.dragWrapper.addEventListener('drop', this.handleDrop.bind(this), false);
        }

        preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        highlight() {
            this.dragWrapper.classList.add('drag-over');
        }

        unhighlight() {
            this.dragWrapper.classList.remove('drag-over');
        }

        handleDrop(e) {
            const files = e.dataTransfer.files;
            this.handleFileSelection(files);
        }

        handleFileSelection(files) {
            console.log('FileManager handleFileSelection called:', {
                lectureId: this.lectureId,
                filesCount: files ? files.length : 0,
                files: files ? Array.from(files).map(f => f.name) : []
            });

            if (!files || files.length === 0) {
                this.clearPreview();
                return;
            }

            this.selectedFiles = Array.from(files);
            this.updateFileInput();
            this.showPreview();
            this.showFeedback(`${files.length} file${files.length > 1 ? 's' : ''} selected`);
        }

        updateFileInput() {
            const dt = new DataTransfer();
            this.selectedFiles.forEach(file => dt.items.add(file));
            this.fileInput.files = dt.files;
        }

        showPreview() {
            this.previewContainer.innerHTML = '';

            if (this.selectedFiles.length === 0) return;

            // Create header with upload button
            const header = this.createHeader();
            this.previewContainer.appendChild(header);

            // Create file previews
            this.selectedFiles.forEach((file, index) => {
                const preview = this.createFilePreview(file, index);
                this.previewContainer.appendChild(preview);
            });
        }

        createHeader() {
            const header = document.createElement('div');
            header.className = 'flex items-center justify-between mb-4 p-4 bg-gradient-to-r from-blue-900/30 to-blue-800/30 border border-blue-500/30 rounded-xl';
            header.innerHTML = `
                <div class="flex items-center space-x-3">
                    <i class="fas fa-file-upload text-blue-400 text-xl"></i>
                    <h4 class="text-lg font-semibold text-white">Files Ready for Upload (${this.selectedFiles.length})</h4>
                </div>
                <button onclick="fileManagers['${this.lectureId}'].uploadFiles()"
                        class="bg-gradient-to-r from-green-600 to-green-500 hover:from-green-500 hover:to-green-400 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl flex items-center space-x-3 hover:scale-105 transform">
                    <i class="fas fa-cloud-upload-alt text-xl animate-pulse"></i>
                    <span class="text-lg">Upload All Files</span>
                </button>
            `;
            return header;
        }

        createFilePreview(file, index) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            const fileName = file.name;

            const preview = document.createElement('div');
            preview.className = 'file-preview-item bg-gradient-to-r from-gray-800 to-gray-750 border border-gray-600 rounded-xl p-5 mt-4 shadow-lg hover:shadow-xl transition-all duration-300 hover:border-red-500/30';
            preview.setAttribute('data-file-index', index);

            const isImage = file.type.startsWith('image/');
            const icon = this.getFileIcon(file.type, fileName);

            preview.innerHTML = `
                <div class="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-5">
                    <div class="file-thumbnail w-20 h-20 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center flex-shrink-0 mx-auto sm:mx-0 shadow-md border border-gray-500">
                        ${isImage ? '<i class="fas fa-image text-gray-300 text-xl"></i>' : icon}
                    </div>
                    <div class="flex-1 text-center sm:text-left space-y-2">
                        <div class="space-y-1">
                            <p class="text-white font-semibold text-lg break-words leading-tight">${fileName}</p>
                            <div class="flex items-center justify-center sm:justify-start space-x-3 text-sm">
                                <span class="text-gray-300 bg-gray-700 px-2 py-1 rounded-md">${fileSize} MB</span>
                                <span class="text-blue-400 bg-blue-900/30 px-2 py-1 rounded-md">Ready to upload</span>
                            </div>
                            <div class="progress-container"></div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button onclick="fileManagers['${this.lectureId}'].removeFile(${index})"
                                class="text-red-400 hover:text-red-300 p-3 rounded-xl hover:bg-red-900/30 transition-all duration-200 border-2 border-red-500/50 hover:border-red-400 hover:scale-110 transform shadow-md"
                                title="Remove file">
                            <i class="fas fa-trash-alt text-xl"></i>
                        </button>
                    </div>
                </div>
            `;

            // Handle image preview
            if (isImage) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const thumbnail = preview.querySelector('.file-thumbnail');
                    thumbnail.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-16 h-16 object-cover rounded-lg">`;
                };
                reader.readAsDataURL(file);
            }

            return preview;
        }

        getFileIcon(fileType, fileName) {
            const extension = fileName.split('.').pop().toLowerCase();

            if (fileType.includes('pdf') || extension === 'pdf') {
                return '<i class="fas fa-file-pdf text-red-400 text-2xl"></i>';
            } else if (fileType.includes('word') || ['doc', 'docx'].includes(extension)) {
                return '<i class="fas fa-file-word text-blue-400 text-2xl"></i>';
            } else if (fileType.includes('excel') || ['xls', 'xlsx'].includes(extension)) {
                return '<i class="fas fa-file-excel text-green-400 text-2xl"></i>';
            } else if (fileType.includes('powerpoint') || ['ppt', 'pptx'].includes(extension)) {
                return '<i class="fas fa-file-powerpoint text-orange-400 text-2xl"></i>';
            } else if (['zip', 'rar', '7z'].includes(extension)) {
                return '<i class="fas fa-file-archive text-yellow-400 text-2xl"></i>';
            } else {
                return '<i class="fas fa-file text-gray-400 text-2xl"></i>';
            }
        }

        removeFile(index) {
            this.selectedFiles.splice(index, 1);
            this.updateFileInput();
            this.showPreview();

            if (this.selectedFiles.length === 0) {
                this.clearPreview();
                this.showFeedback('All files removed');
            } else {
                this.showFeedback('File removed');
            }
        }

        clearPreview() {
            this.previewContainer.innerHTML = '';
            this.selectedFiles = [];
        }

        showFeedback(message) {
            showSaveStatus('info', message);
        }

        async uploadFiles() {
            if (this.selectedFiles.length === 0) {
                this.showFeedback('No files selected for upload');
                return;
            }

            try {
                this.showUploadProgress();

                // Create FormData for the upload
                const formData = new FormData();

                // Add lecture data
                formData.append('type', 'resource');

                // Add files
                this.selectedFiles.forEach((file, index) => {
                    formData.append(`resource_file[]`, file);
                });

                // Upload with progress tracking
                const response = await this.uploadWithProgress(formData);

                if (response.success) {
                    this.showUploadSuccess(response.files || []);
                    this.clearPreview();
                    this.showFeedback('Files uploaded successfully!');

                    // Update the displayed files in the UI
                    this.updateDisplayedFiles(response.files || []);
                } else {
                    throw new Error(response.message || 'Upload failed');
                }
            } catch (error) {
                console.error('Upload failed:', error);
                this.showUploadError();
                this.showFeedback('Upload failed. Please try again.');
            }
        }

        async uploadWithProgress(formData) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();

                // Track upload progress
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        this.updateUploadProgress(percentComplete);
                    }
                });

                xhr.addEventListener('load', () => {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (error) {
                            reject(new Error('Invalid response format'));
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                });

                xhr.addEventListener('error', () => {
                    reject(new Error('Network error occurred'));
                });

                // Get CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

                xhr.open('POST', `/instructor/course-builder/${courseId}/chapters/dummy/lectures/${this.lectureId}/auto-save`);
                xhr.setRequestHeader('X-CSRF-TOKEN', csrfToken);
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

                xhr.send(formData);
            });
        }

        updateUploadProgress(percentage) {
            // Update progress bars in file previews
            const progressBars = this.previewContainer.querySelectorAll('.upload-progress-bar');
            progressBars.forEach(bar => {
                bar.classList.remove('hidden');
                const fill = bar.querySelector('.progress-fill');
                if (fill) {
                    fill.style.width = `${percentage}%`;
                }
            });

            // Update upload button
            const uploadButton = this.previewContainer.querySelector('button');
            if (uploadButton) {
                uploadButton.innerHTML = `
                    <i class="fas fa-spinner fa-spin text-xl"></i>
                    <span class="text-lg">Uploading... ${Math.round(percentage)}%</span>
                `;
            }
        }

        showUploadProgress() {
            // Add progress bars to file previews
            const filePreviews = this.previewContainer.querySelectorAll('.file-preview-item');
            filePreviews.forEach(preview => {
                const progressContainer = preview.querySelector('.progress-container');
                if (progressContainer) {
                    progressContainer.innerHTML = `
                        <div class="upload-progress-bar w-full bg-gray-700 rounded-full h-2 mt-2">
                            <div class="progress-fill bg-gradient-to-r from-blue-500 to-blue-400 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                    `;
                }
            });

            const uploadButton = this.previewContainer.querySelector('button');
            if (uploadButton) {
                uploadButton.disabled = true;
                uploadButton.innerHTML = `
                    <i class="fas fa-spinner fa-spin text-xl"></i>
                    <span class="text-lg">Uploading...</span>
                `;
            }
        }

        showUploadSuccess(files) {
            const uploadButton = this.previewContainer.querySelector('button');
            if (uploadButton) {
                uploadButton.innerHTML = `
                    <i class="fas fa-check text-xl text-green-400"></i>
                    <span class="text-lg">Upload Complete!</span>
                `;
                uploadButton.className = uploadButton.className.replace('from-green-600 to-green-500', 'from-green-500 to-green-400');
            }

            // Show success animation
            setTimeout(() => {
                this.clearPreview();
            }, 2000);
        }

        showUploadError() {
            const uploadButton = this.previewContainer.querySelector('button');
            if (uploadButton) {
                uploadButton.disabled = false;
                uploadButton.innerHTML = `
                    <i class="fas fa-exclamation-triangle text-xl text-red-400"></i>
                    <span class="text-lg">Upload Failed - Retry</span>
                `;
                uploadButton.className = uploadButton.className.replace('from-green-600 to-green-500', 'from-red-600 to-red-500');
            }
        }

        updateDisplayedFiles(files) {
            // This would update the main file display area
            // For now, we'll trigger a page refresh or update the lecture content
            console.log('Files uploaded successfully:', files);

            // Trigger a refresh of the lecture content to show new files
            if (typeof updateFileDisplayRealTime === 'function') {
                updateFileDisplayRealTime(this.lectureId, files);
            }
        }
    }

    // Global file managers storage
    window.fileManagers = window.fileManagers || {};

    // Legacy function - now redirects to new system
    function showFilePreviewBeforeUpload(lectureId, files) {
        if (!window.fileManagers[lectureId]) {
            window.fileManagers[lectureId] = new FileManager(lectureId);
        }
        window.fileManagers[lectureId].handleFileSelection(files);
    }

    // Legacy function - now redirects to new system
    function createFilePreviewElement(file, index, lectureId) {
        // This function is no longer used - FileManager handles preview creation
        console.warn('createFilePreviewElement is deprecated - use FileManager instead');
        return document.createElement('div');
    }

    function getFileTypeIcon(fileType, fileName) {
        const extension = fileName.split('.').pop().toLowerCase();

        if (fileType.startsWith('image/')) {
            return '<i class="fas fa-image text-blue-400 text-2xl"></i>';
        } else if (fileType === 'application/pdf' || extension === 'pdf') {
            return '<i class="fas fa-file-pdf text-red-400 text-2xl"></i>';
        } else if (fileType.includes('word') || ['doc', 'docx'].includes(extension)) {
            return '<i class="fas fa-file-word text-blue-600 text-2xl"></i>';
        } else if (fileType.includes('excel') || ['xls', 'xlsx'].includes(extension)) {
            return '<i class="fas fa-file-excel text-green-600 text-2xl"></i>';
        } else if (fileType.includes('powerpoint') || ['ppt', 'pptx'].includes(extension)) {
            return '<i class="fas fa-file-powerpoint text-orange-600 text-2xl"></i>';
        } else if (['zip', 'rar', '7z'].includes(extension)) {
            return '<i class="fas fa-file-archive text-purple-400 text-2xl"></i>';
        } else if (fileType.startsWith('video/')) {
            return '<i class="fas fa-file-video text-red-500 text-2xl"></i>';
        } else if (fileType.startsWith('audio/')) {
            return '<i class="fas fa-file-audio text-green-500 text-2xl"></i>';
        } else {
            return '<i class="fas fa-file text-gray-400 text-2xl"></i>';
        }
    }

    function updateFilePreviewProgress(lectureId, fileIndex, progress) {
        const previewContainer = document.getElementById(`file-preview-container-${lectureId}`);
        if (!previewContainer) return;

        const filePreview = previewContainer.querySelector(`[data-file-index="${fileIndex}"]`);
        if (!filePreview) return;

        const progressBar = filePreview.querySelector('.upload-progress-bar');
        const progressFill = filePreview.querySelector('.upload-progress-bar .bg-blue-500');
        const statusElement = filePreview.querySelector('.upload-status');

        if (progressBar && progressFill && statusElement) {
            progressBar.classList.remove('hidden');
            progressFill.style.width = `${progress}%`;

            if (progress < 100) {
                statusElement.innerHTML = `<i class="fas fa-spinner fa-spin mr-1"></i>Uploading ${progress}%`;
                statusElement.className = 'upload-status text-blue-400 text-sm';
            } else {
                statusElement.innerHTML = `<i class="fas fa-check mr-1"></i>Upload complete`;
                statusElement.className = 'upload-status text-green-400 text-sm';
            }
        }
    }

    // Legacy functions - now redirect to new FileManager system
    function clearFilePreview(lectureId) {
        if (window.fileManagers[lectureId]) {
            window.fileManagers[lectureId].clearPreview();
        }
    }

    function removeFileFromPreview(fileIndex) {
        // Find which lecture this belongs to by looking at the DOM
        const previewItem = document.querySelector(`[data-file-index="${fileIndex}"]`);
        if (previewItem) {
            const container = previewItem.closest('[id^="file-preview-container-"]');
            if (container) {
                const lectureId = container.id.replace('file-preview-container-', '');
                if (window.fileManagers[lectureId]) {
                    window.fileManagers[lectureId].removeFile(fileIndex);
                }
            }
        }
    }

    function showUploadButton(lectureId) {
        // Handled by FileManager
    }

    function hideUploadButton(lectureId) {
        // Handled by FileManager
    }

    function uploadAllFiles(lectureId) {
        if (window.fileManagers[lectureId]) {
            window.fileManagers[lectureId].uploadFiles();
        } else {
            showSaveStatus('error', 'File manager not initialized');
        }
    }

    function showUploadStartedFeedback(lectureId, fileCount) {
        // Update the upload button to show it's processing
        const previewContainer = document.getElementById(`file-preview-container-${lectureId}`);
        if (previewContainer) {
            const uploadButton = previewContainer.querySelector('button[onclick*="uploadAllFiles"]');
            if (uploadButton) {
                uploadButton.disabled = true;
                uploadButton.innerHTML = `
                    <i class="fas fa-spinner fa-spin text-xl"></i>
                    <span class="text-lg">Uploading ${fileCount} file${fileCount > 1 ? 's' : ''}...</span>
                `;
                uploadButton.className = uploadButton.className.replace('from-green-600 to-green-500', 'from-blue-600 to-blue-500');
            }
        }

        // Show toast notification
        showSaveStatus('info', `Starting upload of ${fileCount} file${fileCount > 1 ? 's' : ''}...`);
    }

    function showUploadSuccessAnimation(lectureId) {
        // Create a temporary success overlay
        const lectureCard = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        if (lectureCard) {
            const successOverlay = document.createElement('div');
            successOverlay.className = 'absolute inset-0 bg-green-500/20 border-2 border-green-400 rounded-lg flex items-center justify-center z-10';
            successOverlay.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-check-circle text-green-400 text-4xl mb-2 animate-bounce"></i>
                    <p class="text-green-300 font-semibold">Upload Complete!</p>
                </div>
            `;

            lectureCard.style.position = 'relative';
            lectureCard.appendChild(successOverlay);

            // Remove the overlay after 2 seconds
            setTimeout(() => {
                successOverlay.remove();
            }, 2000);
        }
    }

    // CRUD operations
    function addChapter() {
        const formData = new FormData();
        formData.append('title', 'New Chapter');
        formData.append('description', '');
        formData.append('is_free_preview', '0');

        fetch(`/instructor/course-builder/${courseId}/chapters`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addChapterToSidebar(data.data);
                selectItem('chapter', data.data.id);
                showSaveStatus('saved', 'Chapter created');

                // Hide empty state
                document.getElementById('empty-curriculum')?.classList.add('hidden');
                document.getElementById('welcome-state')?.classList.add('hidden');
            } else {
                showSaveStatus('error', data.message || 'Failed to create chapter');
            }
        })
        .catch(error => {
            console.error('Error creating chapter:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function addLecture(chapterId) {
        const formData = new FormData();
        formData.append('title', 'New Lecture');
        formData.append('description', '');
        formData.append('type', 'text');
        formData.append('is_free_preview', '0');
        formData.append('is_mandatory', '1');

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                addLectureToSidebar(chapterId, data.data);
                selectItem('lecture', data.data.id);
                showSaveStatus('saved', 'Lecture created');
            } else {
                showSaveStatus('error', data.message || 'Failed to create lecture');
            }
        })
        .catch(error => {
            console.error('Error creating lecture:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function deleteChapter(chapterId) {
        if (!confirm('Are you sure you want to delete this chapter? This will also delete all lectures in this chapter.')) {
            return;
        }

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove from sidebar
                const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);
                if (chapterElement) {
                    chapterElement.remove();
                }

                // Show welcome state if no chapters left
                if (document.querySelectorAll('.chapter-item').length === 0) {
                    document.getElementById('welcome-state')?.classList.remove('hidden');
                    document.getElementById('empty-curriculum')?.classList.remove('hidden');
                    document.getElementById('chapter-editor')?.classList.add('hidden');
                }

                showSaveStatus('saved', 'Chapter deleted');
            } else {
                showSaveStatus('error', data.message || 'Failed to delete chapter');
            }
        })
        .catch(error => {
            console.error('Error deleting chapter:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function deleteLecture(lectureId) {
        if (!confirm('Are you sure you want to delete this lecture?')) {
            return;
        }

        // Get chapter ID from the lecture element
        const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        const chapterElement = lectureElement?.closest('.chapter-item');
        const chapterId = chapterElement?.getAttribute('data-chapter-id');

        if (!chapterId) return;

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove from sidebar
                if (lectureElement) {
                    lectureElement.remove();
                }

                // Update lecture count
                updateChapterLectureCount(chapterId);

                // Hide lecture editor
                document.getElementById('lecture-editor')?.classList.add('hidden');

                showSaveStatus('saved', 'Lecture deleted');
            } else {
                showSaveStatus('error', data.message || 'Failed to delete lecture');
            }
        })
        .catch(error => {
            console.error('Error deleting lecture:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function reorderChapters() {
        const chapterElements = document.querySelectorAll('.chapter-item');
        const chapterIds = Array.from(chapterElements).map(el => el.getAttribute('data-chapter-id'));

        fetch(`/instructor/course-builder/${courseId}/chapters/reorder`, {
            method: 'POST',
            body: JSON.stringify({ chapter_ids: chapterIds }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveStatus('saved', 'Chapters reordered');
            } else {
                showSaveStatus('error', data.message || 'Failed to reorder chapters');
            }
        })
        .catch(error => {
            console.error('Error reordering chapters:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function reorderLectures(chapterId) {
        const lectureElements = document.querySelectorAll(`#chapter-lectures-${chapterId} .lecture-item`);
        const lectureIds = Array.from(lectureElements).map(el => el.getAttribute('data-lecture-id'));

        fetch(`/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/reorder`, {
            method: 'POST',
            body: JSON.stringify({ lecture_ids: lectureIds }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveStatus('saved', 'Lectures reordered');
            } else {
                showSaveStatus('error', data.message || 'Failed to reorder lectures');
            }
        })
        .catch(error => {
            console.error('Error reordering lectures:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    function togglePublishStatus() {
        const button = document.getElementById('publish-toggle-btn');
        const currentStatus = button.getAttribute('data-current-status');

        fetch(`/instructor/course-builder/${courseId}/toggle-publish`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update button
                const newStatus = data.status;
                button.setAttribute('data-current-status', newStatus);

                if (newStatus === 'published') {
                    button.className = 'px-4 py-2 rounded-lg font-medium transition-colors bg-green-600 hover:bg-green-700 text-white';
                    button.innerHTML = '<i class="fas fa-eye mr-2"></i>Published';
                } else {
                    button.className = 'px-4 py-2 rounded-lg font-medium transition-colors bg-red-600 hover:bg-red-700 text-white';
                    button.innerHTML = '<i class="fas fa-eye-slash mr-2"></i>Publish Course';
                }

                showSaveStatus('saved', data.message);
            } else {
                showSaveStatus('error', data.message || 'Failed to update course status');
            }
        })
        .catch(error => {
            console.error('Error toggling publish status:', error);
            showSaveStatus('error', 'Network error');
        });
    }

    // Helper functions for DOM manipulation
    function addChapterToSidebar(chapter) {
        const curriculumTree = document.getElementById('curriculum-tree');
        const emptyState = document.getElementById('empty-curriculum');

        if (emptyState) {
            emptyState.remove();
        }

        const chapterHtml = `
            <div class="chapter-item bg-gray-800 rounded-lg border border-gray-700"
                 data-chapter-id="${chapter.id}"
                 data-chapter-index="0">
                <!-- Chapter Header -->
                <div class="chapter-header p-4 cursor-pointer hover:bg-gray-750 transition-colors"
                     onclick="selectItem('chapter', '${chapter.id}')">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                                <i class="fas fa-grip-vertical"></i>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-folder text-yellow-500"></i>
                                <span class="text-white font-medium">${chapter.title}</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs text-gray-400">0 lectures</span>
                            <button class="text-gray-400 hover:text-white transition-colors"
                                    onclick="event.stopPropagation(); toggleChapter('${chapter.id}')">
                                <i class="fas fa-chevron-down chapter-toggle"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Chapter Lectures -->
                <div class="chapter-lectures pl-8 pb-2" id="chapter-lectures-${chapter.id}">
                    <!-- Add Lecture Button -->
                    <div class="p-3">
                        <button class="add-lecture-btn w-full text-left text-gray-400 hover:text-white transition-colors text-sm"
                                data-chapter-id="${chapter.id}"
                                onclick="addLecture('${chapter.id}')">
                            <i class="fas fa-plus mr-2"></i>Add Lecture
                        </button>
                    </div>
                </div>
            </div>
        `;

        curriculumTree.insertAdjacentHTML('beforeend', chapterHtml);

        // Initialize drag and drop for the new chapter
        initializeDragAndDrop();
    }

    function addLectureToSidebar(chapterId, lecture) {
        const lecturesContainer = document.getElementById(`chapter-lectures-${chapterId}`);
        const addButton = lecturesContainer.querySelector('.add-lecture-btn').parentElement;

        const lectureHtml = `
            <div class="lecture-item p-3 hover:bg-gray-750 rounded cursor-pointer transition-colors"
                 data-lecture-id="${lecture.id}"
                 data-lecture-index="0"
                 onclick="selectItem('lecture', '${lecture.id}')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="drag-handle cursor-move text-gray-500 hover:text-gray-300">
                            <i class="fas fa-grip-vertical text-xs"></i>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="${getLectureIcon(lecture.type)}"></i>
                            <span class="text-gray-300 text-sm">${lecture.title}</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        ${lecture.duration_minutes ? `<span class="text-xs text-gray-500">${lecture.duration_minutes}min</span>` : ''}
                        ${lecture.is_free_preview ? '<span class="text-xs bg-green-600 text-white px-2 py-1 rounded">Preview</span>' : ''}
                    </div>
                </div>
            </div>
        `;

        addButton.insertAdjacentHTML('beforebegin', lectureHtml);

        // Update chapter lecture count
        updateChapterLectureCount(chapterId);

        // Initialize drag and drop for the new lecture
        initializeDragAndDrop();
    }

    function updateChapterLectureCount(chapterId) {
        const chapterElement = document.querySelector(`[data-chapter-id="${chapterId}"]`);
        const lectureCount = document.querySelectorAll(`#chapter-lectures-${chapterId} .lecture-item`).length;
        const countSpan = chapterElement?.querySelector('.text-xs.text-gray-400');

        if (countSpan) {
            countSpan.textContent = `${lectureCount} lecture${lectureCount !== 1 ? 's' : ''}`;
        }
    }

    function toggleChapter(chapterId) {
        const lecturesContainer = document.getElementById(`chapter-lectures-${chapterId}`);
        const toggleIcon = document.querySelector(`[data-chapter-id="${chapterId}"] .chapter-toggle`);

        if (lecturesContainer && toggleIcon) {
            if (lecturesContainer.style.display === 'none') {
                lecturesContainer.style.display = 'block';
                toggleIcon.classList.remove('fa-chevron-right');
                toggleIcon.classList.add('fa-chevron-down');
            } else {
                lecturesContainer.style.display = 'none';
                toggleIcon.classList.remove('fa-chevron-down');
                toggleIcon.classList.add('fa-chevron-right');
            }
        }
    }

    // Global functions for onclick handlers
    window.selectItem = selectItem;
    window.addLecture = addLecture;
    window.deleteChapter = deleteChapter;
    window.deleteLecture = deleteLecture;
    window.toggleChapter = toggleChapter;
    window.toggleLectureContent = toggleLectureContent;
    window.saveCourse = saveCourse;
    window.saveChapter = saveChapter;
    window.saveLecture = saveLecture;
    window.uploadAllFiles = uploadAllFiles;
    window.removeFileFromPreview = removeFileFromPreview;
    window.confirmRemoveFile = confirmRemoveFile;
    window.removeFile = removeFile;
    window.closeConfirmDialog = closeConfirmDialog;
});
</script>

<style>
.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    transform: scale(1.02);
}

.sortable-drag {
    transform: rotate(5deg);
}

.lecture-content.hidden {
    display: none !important;
}

.auto-save-indicator {
    transition: all 0.3s ease;
}

.chapter-item.bg-red-900 {
    background-color: rgba(127, 29, 29, 0.5) !important;
}

.lecture-item.bg-red-900 {
    background-color: rgba(127, 29, 29, 0.3) !important;
}

/* Mobile/Tablet-specific styles */
@media (max-width: 1023px) {
    /* Ensure mobile sidebar is properly positioned */
    #curriculum-sidebar {
        top: 0;
        bottom: 0;
        height: 100vh;
        max-height: 100vh;
    }

    /* Mobile touch targets */
    .chapter-item, .lecture-item {
        min-height: 48px; /* Minimum touch target size */
    }

    /* Mobile button sizing */
    button {
        min-height: 44px; /* iOS recommended touch target */
        min-width: 44px;
    }

    /* Mobile form inputs */
    input, textarea, select {
        font-size: 16px; /* Prevent zoom on iOS */
        min-height: 44px;
    }

    /* Mobile spacing adjustments */
    .chapter-header {
        padding: 1rem !important;
    }

    .lecture-item {
        padding: 0.75rem !important;
    }

    /* Mobile text sizing */
    .chapter-item .text-white {
        font-size: 0.95rem;
    }

    .lecture-item .text-gray-300 {
        font-size: 0.875rem;
    }

    /* Hide drag handles on mobile for cleaner look */
    .drag-handle {
        display: none;
    }

    /* Mobile-friendly scrolling */
    .overflow-y-auto {
        -webkit-overflow-scrolling: touch;
    }
}

/* Note: Tablets now use the same mobile layout (sidebar hidden) for better content space */

/* Smooth transitions for sidebar */
#curriculum-sidebar {
    transition: transform 0.3s ease-in-out;
}

/* Prevent body scroll when mobile sidebar is open */
body.overflow-hidden {
    overflow: hidden;
    position: fixed;
    width: 100%;
}

/* Mobile overlay styling */
#mobile-sidebar-overlay {
    backdrop-filter: blur(2px);
}

/* Touch feedback */
.touch-feedback {
    background-color: rgba(127, 29, 29, 0.2);
    transition: background-color 0.15s ease;
}

/* File preview animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Enhanced file preview styling */
.file-preview-item {
    position: relative;
    overflow: hidden;
}

.file-preview-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.file-preview-item:hover::before {
    left: 100%;
}

/* Progress bar enhancements */
.upload-progress-bar .bg-gradient-to-r {
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* File thumbnail hover effects */
.file-thumbnail {
    transition: all 0.3s ease;
}

.file-preview-item:hover .file-thumbnail {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Button hover animations */
button:hover {
    transform: translateY(-1px);
}

/* Success feedback animations */
@keyframes successPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

.success-animation {
    animation: successPulse 0.6s ease-in-out;
}

/* File upload progress enhancements */
.upload-progress-bar {
    position: relative;
    overflow: hidden;
}

.upload-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Enhanced file input styling */
.file-input-wrapper {
    position: relative;
    overflow: hidden;
}

.file-input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
    transition: left 0.6s;
}

.file-input-wrapper:hover::before {
    left: 100%;
}

/* File input drag and drop effects */
.file-input-wrapper.drag-over {
    border-color: #22c55e !important;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    transform: scale(1.02);
}

/* Icon animations */
@keyframes iconBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

.file-input-wrapper:hover .fas.fa-cloud-upload-alt {
    animation: iconBounce 1s ease-in-out infinite;
}
</style>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\escapematrix\resources\views/instructor/course-builder/show.blade.php ENDPATH**/ ?>