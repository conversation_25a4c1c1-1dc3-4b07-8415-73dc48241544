<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Manager Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">File Manager Test</h1>
        
        <!-- Test File Input -->
        <div class="bg-gray-800 p-6 rounded-xl mb-8">
            <h2 class="text-xl font-semibold mb-4">File Input Test</h2>
            
            <div class="file-input-wrapper relative border-2 border-dashed border-gray-600 hover:border-green-500 rounded-xl p-6 bg-gradient-to-br from-gray-800 to-gray-750 hover:from-gray-750 hover:to-gray-700 transition-all duration-300 cursor-pointer group">
                <input type="file" name="resource_file" multiple
                       class="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                       accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.jpg,.jpeg,.png,.gif,.webp">

                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 group-hover:text-green-400 transition-colors duration-300"></i>
                    </div>
                    <p class="text-lg font-semibold text-gray-300 group-hover:text-white transition-colors duration-300 mb-2">
                        <i class="fas fa-plus mr-2 text-green-400"></i>
                        Click to select files or drag & drop
                    </p>
                    <p class="text-sm text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                        Multiple files supported
                    </p>
                </div>
            </div>
            
            <!-- File Preview Container -->
            <div id="file-preview-container-test123" class="mt-3"></div>
        </div>
        
        <!-- Test Results -->
        <div class="bg-gray-800 p-6 rounded-xl">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="test-results" class="space-y-2 text-sm font-mono"></div>
        </div>
    </div>

    <script>
        // Copy the FileManager class from the course builder
        class FileManager {
            constructor(lectureId) {
                this.lectureId = lectureId;
                this.selectedFiles = [];
                this.fileInput = null;
                this.previewContainer = null;
                this.dragWrapper = null;
                this.init();
            }

            init() {
                this.fileInput = document.querySelector(`input[name="resource_file"]`);
                this.previewContainer = document.getElementById(`file-preview-container-${this.lectureId}`);
                this.dragWrapper = document.querySelector('.file-input-wrapper');
                
                console.log('FileManager init for lecture:', this.lectureId, {
                    fileInput: !!this.fileInput,
                    previewContainer: !!this.previewContainer,
                    dragWrapper: !!this.dragWrapper
                });
                
                if (!this.fileInput || !this.previewContainer || !this.dragWrapper) {
                    console.error('FileManager: Required elements not found', {
                        fileInput: !!this.fileInput,
                        previewContainer: !!this.previewContainer,
                        dragWrapper: !!this.dragWrapper
                    });
                    return;
                }

                this.setupEventListeners();
                console.log('FileManager successfully initialized for lecture:', this.lectureId);
            }

            setupEventListeners() {
                // File input change event
                this.fileInput.addEventListener('change', (e) => {
                    this.handleFileSelection(e.target.files);
                });

                // Drag and drop events
                ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                    this.dragWrapper.addEventListener(eventName, this.preventDefaults.bind(this), false);
                });

                ['dragenter', 'dragover'].forEach(eventName => {
                    this.dragWrapper.addEventListener(eventName, this.highlight.bind(this), false);
                });

                ['dragleave', 'drop'].forEach(eventName => {
                    this.dragWrapper.addEventListener(eventName, this.unhighlight.bind(this), false);
                });

                this.dragWrapper.addEventListener('drop', this.handleDrop.bind(this), false);
            }

            preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            highlight() {
                this.dragWrapper.classList.add('border-green-500');
                this.dragWrapper.classList.remove('border-gray-600');
            }

            unhighlight() {
                this.dragWrapper.classList.remove('border-green-500');
                this.dragWrapper.classList.add('border-gray-600');
            }

            handleDrop(e) {
                const files = e.dataTransfer.files;
                this.handleFileSelection(files);
            }

            handleFileSelection(files) {
                console.log('FileManager handleFileSelection called:', {
                    lectureId: this.lectureId,
                    filesCount: files ? files.length : 0,
                    files: files ? Array.from(files).map(f => f.name) : []
                });

                this.logResult(`Files selected: ${files ? files.length : 0}`);

                if (!files || files.length === 0) {
                    this.clearPreview();
                    return;
                }

                this.selectedFiles = Array.from(files);
                this.updateFileInput();
                this.showPreview();
                this.logResult(`Preview shown for ${files.length} files`);
            }

            updateFileInput() {
                const dt = new DataTransfer();
                this.selectedFiles.forEach(file => dt.items.add(file));
                this.fileInput.files = dt.files;
            }

            showPreview() {
                this.previewContainer.innerHTML = '';
                
                if (this.selectedFiles.length === 0) return;

                // Create header
                const header = document.createElement('div');
                header.className = 'flex items-center justify-between mb-4 p-4 bg-gradient-to-r from-blue-900/30 to-blue-800/30 border border-blue-500/30 rounded-xl';
                header.innerHTML = `
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-file-upload text-blue-400 text-xl"></i>
                        <h4 class="text-lg font-semibold text-white">Files Ready (${this.selectedFiles.length})</h4>
                    </div>
                    <button onclick="testFileManager.uploadFiles()"
                            class="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded-lg">
                        Test Upload
                    </button>
                `;
                this.previewContainer.appendChild(header);

                // Create file previews
                this.selectedFiles.forEach((file, index) => {
                    const preview = this.createFilePreview(file, index);
                    this.previewContainer.appendChild(preview);
                });
            }

            createFilePreview(file, index) {
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                const fileName = file.name;
                
                const preview = document.createElement('div');
                preview.className = 'bg-gray-700 border border-gray-600 rounded-lg p-4 mt-2';
                preview.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-white font-semibold">${fileName}</p>
                            <p class="text-gray-400 text-sm">${fileSize} MB</p>
                        </div>
                        <button onclick="testFileManager.removeFile(${index})"
                                class="text-red-400 hover:text-red-300 px-3 py-1 rounded">
                            Remove
                        </button>
                    </div>
                `;
                return preview;
            }

            removeFile(index) {
                this.selectedFiles.splice(index, 1);
                this.updateFileInput();
                this.showPreview();
                this.logResult(`File removed. Remaining: ${this.selectedFiles.length}`);
            }

            clearPreview() {
                this.previewContainer.innerHTML = '';
                this.selectedFiles = [];
            }

            uploadFiles() {
                this.logResult(`Upload test: ${this.selectedFiles.length} files would be uploaded`);
                this.selectedFiles.forEach((file, index) => {
                    this.logResult(`  - ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
                });
            }

            logResult(message) {
                const results = document.getElementById('test-results');
                const timestamp = new Date().toLocaleTimeString();
                const div = document.createElement('div');
                div.className = 'text-green-400';
                div.textContent = `[${timestamp}] ${message}`;
                results.appendChild(div);
                results.scrollTop = results.scrollHeight;
            }
        }

        // Initialize test
        window.testFileManager = new FileManager('test123');
        
        // Log initialization
        testFileManager.logResult('FileManager test initialized');
    </script>
</body>
</html>
