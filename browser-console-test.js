// File Manager Test - Run this in the browser console on the course builder page

console.log('🧪 Starting File Manager Test...');

// Test 1: Check if FileManager class exists
if (typeof FileManager === 'undefined') {
    console.error('❌ FileManager class not found');
} else {
    console.log('✅ FileManager class found');
}

// Test 2: Find file inputs
const fileInputs = document.querySelectorAll('input[name="resource_file"]');
console.log(`📁 Found ${fileInputs.length} file input(s)`);

// Test 3: Check for preview containers
const previewContainers = document.querySelectorAll('[id^="file-preview-container-"]');
console.log(`📋 Found ${previewContainers.length} preview container(s)`);

// Test 4: Check for drag wrappers
const dragWrappers = document.querySelectorAll('.file-input-wrapper');
console.log(`🎯 Found ${dragWrappers.length} drag wrapper(s)`);

// Test 5: Check existing FileManagers
if (window.fileManagers) {
    const managerCount = Object.keys(window.fileManagers).length;
    console.log(`🔧 Found ${managerCount} existing FileManager(s)`);
    Object.keys(window.fileManagers).forEach(id => {
        console.log(`  - FileManager for lecture: ${id}`);
    });
} else {
    console.log('⚠️ No FileManagers found');
}

// Test 6: Try to create a FileManager manually
if (fileInputs.length > 0 && previewContainers.length > 0) {
    const testLectureId = 'test-' + Date.now();
    
    try {
        if (!window.fileManagers) window.fileManagers = {};
        window.fileManagers[testLectureId] = new FileManager(testLectureId);
        console.log('✅ Successfully created test FileManager');
        
        // Test file selection
        const testFiles = [
            new File(['test content 1'], 'test-document.pdf', { type: 'application/pdf' }),
            new File(['test content 2'], 'test-image.jpg', { type: 'image/jpeg' })
        ];
        
        window.fileManagers[testLectureId].handleFileSelection(testFiles);
        console.log('✅ File selection test completed');
        
        // Test file removal
        if (window.fileManagers[testLectureId].selectedFiles.length > 0) {
            window.fileManagers[testLectureId].removeFile(0);
            console.log('✅ File removal test completed');
        }
        
    } catch (error) {
        console.error('❌ Error creating test FileManager:', error);
    }
} else {
    console.log('⚠️ Cannot create test FileManager - missing required elements');
}

// Test 7: Check for resource lectures
const resourceLectures = document.querySelectorAll('[data-type="resource"], .lecture-item');
console.log(`📚 Found ${resourceLectures.length} lecture(s) that could be resource type`);

if (resourceLectures.length > 0) {
    console.log('🎯 Clicking on first lecture to test initialization...');
    resourceLectures[0].click();
    
    setTimeout(() => {
        // Check if FileManager was initialized after clicking
        const newManagerCount = window.fileManagers ? Object.keys(window.fileManagers).length : 0;
        console.log(`🔧 FileManagers after click: ${newManagerCount}`);
        
        // Look for the specific elements that should be present
        const activeFileInput = document.querySelector('input[name="resource_file"]:not([style*="display: none"])');
        if (activeFileInput) {
            console.log('✅ Active file input found');
            
            // Try to trigger file selection
            const testFiles = [new File(['test'], 'test.pdf', { type: 'application/pdf' })];
            const event = new Event('change', { bubbles: true });
            
            // Create a DataTransfer object and add files
            const dt = new DataTransfer();
            testFiles.forEach(file => dt.items.add(file));
            activeFileInput.files = dt.files;
            
            // Dispatch the change event
            activeFileInput.dispatchEvent(event);
            console.log('✅ File selection event dispatched');
            
        } else {
            console.log('⚠️ No active file input found');
        }
        
        console.log('🎉 File Manager Test Complete!');
    }, 2000);
} else {
    console.log('🎉 File Manager Test Complete!');
}

// Helper function to run a quick file upload test
window.testFileUpload = function() {
    console.log('📤 Testing file upload...');
    
    if (!window.fileManagers || Object.keys(window.fileManagers).length === 0) {
        console.log('❌ No FileManagers available');
        return;
    }
    
    const lectureId = Object.keys(window.fileManagers)[0];
    const fm = window.fileManagers[lectureId];
    
    if (fm.selectedFiles.length === 0) {
        const testFiles = [new File(['test content'], 'test-upload.pdf', { type: 'application/pdf' })];
        fm.handleFileSelection(testFiles);
        console.log('✅ Test files added');
    }
    
    // Mock the upload to avoid actual server call
    const originalUpload = fm.uploadWithProgress;
    fm.uploadWithProgress = async (formData) => {
        console.log('📤 Mock upload called');
        return { success: true, files: [] };
    };
    
    fm.uploadFiles().then(() => {
        console.log('✅ Upload test completed');
        fm.uploadWithProgress = originalUpload; // Restore original
    }).catch(error => {
        console.error('❌ Upload test failed:', error);
        fm.uploadWithProgress = originalUpload; // Restore original
    });
};

console.log('📋 Available test functions:');
console.log('  - testFileUpload() - Test file upload functionality');
