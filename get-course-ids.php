<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Course;
use App\Models\Lecture;

echo "=== COURSES ===\n";
$courses = Course::select('id', 'title')->get();
foreach ($courses as $course) {
    echo $course->id . " - " . $course->title . "\n";
}

echo "\n=== RESOURCE LECTURES ===\n";
$resourceLectures = Lecture::where('type', 'resource')
    ->with(['course:id,title', 'chapter:id,title'])
    ->select('id', 'title', 'course_id', 'chapter_id')
    ->get();

foreach ($resourceLectures as $lecture) {
    echo $lecture->id . " - " . $lecture->title . " (Course: " . $lecture->course->title . ")\n";
}
